#!/bin/bash

# 订单服务测试脚本
# 测试订单服务的核心功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8083/api/orders"
USER_SERVICE_URL="http://localhost:8081/api/users"
PRODUCT_SERVICE_URL="http://localhost:8082/api/products"
TEST_USER_USERNAME="testuser$(date +%s)"
TEST_USER_EMAIL="test$(date +%s)@example.com"
TEST_USER_PASSWORD="Password123"
ACCESS_TOKEN=""
PRODUCT_ID=""
ORDER_ID=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否启动
check_service() {
    log_info "检查订单服务是否启动..."
    
    for i in {1..30}; do
        if curl -s -f "$BASE_URL/health" > /dev/null 2>&1; then
            log_success "订单服务已启动"
            return 0
        fi
        log_info "等待服务启动... ($i/30)"
        sleep 2
    done
    
    log_error "订单服务启动超时"
    return 1
}

# 获取用户访问令牌
get_user_token() {
    log_info "获取用户访问令牌..."
    
    # 先注册测试用户
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER_USERNAME\",
            \"email\": \"$TEST_USER_EMAIL\",
            \"password\": \"$TEST_USER_PASSWORD\",
            \"confirmPassword\": \"$TEST_USER_PASSWORD\"
        }" \
        "$USER_SERVICE_URL/auth/register" > /dev/null
    
    # 登录获取令牌
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"usernameOrEmail\": \"$TEST_USER_USERNAME\",
            \"password\": \"$TEST_USER_PASSWORD\"
        }" \
        "$USER_SERVICE_URL/auth/login")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        ACCESS_TOKEN=$(echo "$response_body" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$ACCESS_TOKEN" ]; then
            log_success "获取到访问令牌: ${ACCESS_TOKEN:0:20}..."
        else
            log_warning "未能提取访问令牌"
        fi
    else
        log_error "获取访问令牌失败"
        return 1
    fi
}

# 获取测试商品ID
get_test_product() {
    log_info "获取测试商品ID..."
    
    response=$(curl -s -w "%{http_code}" "$PRODUCT_SERVICE_URL/?page=0&size=1")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        PRODUCT_ID=$(echo "$response_body" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
        if [ -n "$PRODUCT_ID" ]; then
            log_success "获取到商品ID: $PRODUCT_ID"
        else
            log_warning "未能获取商品ID"
        fi
    else
        log_error "获取商品失败"
        return 1
    fi
}

# 测试健康检查
test_health_check() {
    log_info "测试健康检查接口..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/health")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "健康检查通过"
        echo "响应: ${response%???}"
    else
        log_error "健康检查失败，HTTP状态码: $http_code"
        return 1
    fi
}

# 测试获取购物车
test_get_cart() {
    log_info "测试获取用户购物车..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/cart")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取购物车成功"
        echo "响应: $response_body"
    else
        log_error "获取购物车失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试添加商品到购物车
test_add_to_cart() {
    log_info "测试添加商品到购物车..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$PRODUCT_ID" ]; then
        log_error "没有访问令牌或商品ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{
            \"productId\": $PRODUCT_ID,
            \"quantity\": 2
        }" \
        "$BASE_URL/cart/items")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "添加商品到购物车成功"
        echo "响应: $response_body"
    else
        log_error "添加商品到购物车失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试更新购物车商品数量
test_update_cart_item() {
    log_info "测试更新购物车商品数量..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$PRODUCT_ID" ]; then
        log_error "没有访问令牌或商品ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/cart/items/$PRODUCT_ID?quantity=3")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "更新购物车商品数量成功"
        echo "响应: $response_body"
    else
        log_error "更新购物车商品数量失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取购物车商品数量
test_get_cart_count() {
    log_info "测试获取购物车商品数量..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/cart/count")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取购物车商品数量成功"
        echo "响应: $response_body"
    else
        log_error "获取购物车商品数量失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试从购物车创建订单
test_create_order_from_cart() {
    log_info "测试从购物车创建订单..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{
            \"paymentMethod\": \"ALIPAY\",
            \"notes\": \"测试订单\",
            \"shippingName\": \"张三\",
            \"shippingPhone\": \"13900139000\",
            \"shippingAddress\": \"某某街道123号\",
            \"shippingProvince\": \"北京市\",
            \"shippingCity\": \"北京市\",
            \"shippingDistrict\": \"朝阳区\"
        }" \
        "$BASE_URL/from-cart")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "201" ]; then
        log_success "从购物车创建订单成功"
        # 提取订单ID
        ORDER_ID=$(echo "$response_body" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
        if [ -n "$ORDER_ID" ]; then
            log_success "获取到订单ID: $ORDER_ID"
        fi
        echo "响应: $response_body"
    else
        log_error "从购物车创建订单失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取用户订单列表
test_get_user_orders() {
    log_info "测试获取用户订单列表..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/user?page=0&size=10")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取用户订单列表成功"
        echo "响应: $response_body"
    else
        log_error "获取用户订单列表失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试根据ID获取订单详情
test_get_order_by_id() {
    log_info "测试根据ID获取订单详情..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$ORDER_ID" ]; then
        log_error "没有访问令牌或订单ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/$ORDER_ID")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取订单详情成功"
        echo "响应: $response_body"
    else
        log_error "获取订单详情失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试模拟支付订单
test_pay_order() {
    log_info "测试模拟支付订单..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$ORDER_ID" ]; then
        log_error "没有访问令牌或订单ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/$ORDER_ID/payment?paymentMethod=ALIPAY&transactionId=TEST_TXN_$(date +%s)")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "支付订单成功"
        echo "响应: $response_body"
    else
        log_error "支付订单失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试检查订单是否可以取消
test_can_cancel_order() {
    log_info "测试检查订单是否可以取消..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$ORDER_ID" ]; then
        log_error "没有访问令牌或订单ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/$ORDER_ID/can-cancel")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "检查订单取消状态成功"
        echo "响应: $response_body"
    else
        log_error "检查订单取消状态失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取用户订单统计
test_get_user_order_statistics() {
    log_info "测试获取用户订单统计..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/user/statistics")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取用户订单统计成功"
        echo "响应: $response_body"
    else
        log_error "获取用户订单统计失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试从购物车移除商品
test_remove_from_cart() {
    log_info "测试从购物车移除商品..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$PRODUCT_ID" ]; then
        log_error "没有访问令牌或商品ID，跳过测试"
        return 1
    fi
    
    # 先添加商品到购物车
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{\"productId\": $PRODUCT_ID, \"quantity\": 1}" \
        "$BASE_URL/cart/items" > /dev/null
    
    # 然后移除商品
    response=$(curl -s -w "%{http_code}" -X DELETE \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/cart/items/$PRODUCT_ID")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "从购物车移除商品成功"
        echo "响应: $response_body"
    else
        log_error "从购物车移除商品失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 主测试函数
main() {
    echo "=========================================="
    echo "         订单服务功能测试"
    echo "=========================================="
    echo ""
    
    # 检查服务状态
    if ! check_service; then
        log_error "服务未启动，请先启动订单服务"
        exit 1
    fi
    
    # 获取访问令牌
    if ! get_user_token; then
        log_error "获取访问令牌失败"
        exit 1
    fi
    
    # 获取测试商品
    if ! get_test_product; then
        log_error "获取测试商品失败"
        exit 1
    fi
    
    echo ""
    echo "开始执行测试用例..."
    echo ""
    
    # 执行测试用例
    test_health_check
    echo ""
    
    test_get_cart
    echo ""
    
    test_add_to_cart
    echo ""
    
    test_update_cart_item
    echo ""
    
    test_get_cart_count
    echo ""
    
    test_create_order_from_cart
    echo ""
    
    test_get_user_orders
    echo ""
    
    test_get_order_by_id
    echo ""
    
    test_pay_order
    echo ""
    
    test_can_cancel_order
    echo ""
    
    test_get_user_order_statistics
    echo ""
    
    test_remove_from_cart
    echo ""
    
    echo "=========================================="
    log_success "所有测试用例执行完成！"
    echo "=========================================="
}

# 执行主函数
main "$@"
