#!/bin/bash

# 演示测试脚本
# 展示如何使用测试套件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[DEMO]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "=========================================="
    echo "    云原生电商平台测试套件演示"
    echo "=========================================="
    echo ""
    echo "本演示将展示如何使用测试脚本来验证微服务功能"
    echo ""
}

# 显示测试选项
show_options() {
    echo "请选择要执行的操作："
    echo ""
    echo "1. 检查所有服务状态"
    echo "2. 测试用户服务 (user-service)"
    echo "3. 测试商品服务 (product-service)"
    echo "4. 测试订单服务 (order-service)"
    echo "5. 测试所有服务"
    echo "6. 查看测试结果"
    echo "7. 清理测试结果"
    echo "0. 退出"
    echo ""
}

# 检查服务状态
check_services() {
    log_header "检查所有服务状态..."
    echo ""
    
    ./test-all-services.sh --check-only
    
    echo ""
    read -p "按回车键继续..."
}

# 测试单个服务
test_single_service() {
    local service=$1
    log_header "测试 $service..."
    echo ""
    
    case $service in
        "user-service")
            ./test-user-service.sh
            ;;
        "product-service")
            ./test-product-service.sh
            ;;
        "order-service")
            ./test-order-service.sh
            ;;
        *)
            log_error "未知服务: $service"
            return 1
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
}

# 测试所有服务
test_all_services() {
    log_header "测试所有服务..."
    echo ""
    
    ./test-all-services.sh
    
    echo ""
    read -p "按回车键继续..."
}

# 查看测试结果
view_results() {
    log_header "查看测试结果..."
    echo ""
    
    if [ -d "results" ]; then
        log_info "测试结果目录内容："
        ls -la results/
        echo ""
        
        # 查找最新的测试报告
        latest_report=$(ls -t results/test-report-*.md 2>/dev/null | head -1)
        if [ -n "$latest_report" ]; then
            log_info "最新测试报告: $latest_report"
            echo ""
            echo "是否查看报告内容？(y/n)"
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                cat "$latest_report"
            fi
        else
            log_warning "未找到测试报告文件"
        fi
    else
        log_warning "测试结果目录不存在，请先运行测试"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 清理测试结果
cleanup_results() {
    log_header "清理测试结果..."
    echo ""
    
    if [ -d "results" ]; then
        echo "确定要删除所有测试结果吗？(y/n)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -rf results/
            log_success "测试结果已清理"
        else
            log_info "取消清理操作"
        fi
    else
        log_info "测试结果目录不存在，无需清理"
    fi
    
    echo ""
    read -p "按回车键继续..."
}

# 显示使用提示
show_tips() {
    echo ""
    log_header "使用提示："
    echo ""
    echo "1. 在运行测试之前，请确保所有微服务都已启动"
    echo "2. 测试会使用随机数据，不会影响现有数据"
    echo "3. 如果测试失败，请检查服务日志和网络连接"
    echo "4. 测试结果会保存在 results/ 目录中"
    echo "5. 可以使用 Ctrl+C 随时中断测试"
    echo ""
}

# 主循环
main() {
    show_welcome
    show_tips
    
    while true; do
        show_options
        read -p "请输入选项 (0-7): " choice
        echo ""
        
        case $choice in
            1)
                check_services
                ;;
            2)
                test_single_service "user-service"
                ;;
            3)
                test_single_service "product-service"
                ;;
            4)
                test_single_service "order-service"
                ;;
            5)
                test_all_services
                ;;
            6)
                view_results
                ;;
            7)
                cleanup_results
                ;;
            0)
                log_success "感谢使用云原生电商平台测试套件！"
                exit 0
                ;;
            *)
                log_error "无效选项，请重新选择"
                echo ""
                ;;
        esac
    done
}

# 检查脚本是否在正确的目录中运行
if [ ! -f "test-all-services.sh" ]; then
    log_error "请在 scripts/testing/ 目录中运行此脚本"
    exit 1
fi

# 执行主函数
main "$@"
