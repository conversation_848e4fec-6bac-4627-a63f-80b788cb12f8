#!/bin/bash

# 全服务测试脚本
# 测试所有微服务的核心功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_RESULTS_DIR="$SCRIPT_DIR/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 服务配置
declare -A SERVICES=(
    ["user-service"]="8081"
    ["product-service"]="8082"
    ["order-service"]="8083"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

log_section() {
    echo -e "${CYAN}[SECTION]${NC} $1"
}

# 创建结果目录
create_results_dir() {
    mkdir -p "$TEST_RESULTS_DIR"
    log_info "测试结果将保存到: $TEST_RESULTS_DIR"
}

# 检查所有服务状态
check_all_services() {
    log_header "检查所有服务状态..."
    echo ""
    
    local all_services_up=true
    
    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        local health_url="http://localhost:$port/api/${service%%-*}/health"
        
        log_info "检查 $service (端口: $port)..."
        
        if curl -s -f "$health_url" > /dev/null 2>&1; then
            log_success "$service 运行正常"
        else
            log_error "$service 未运行或健康检查失败"
            all_services_up=false
        fi
    done
    
    echo ""
    
    if [ "$all_services_up" = true ]; then
        log_success "所有服务运行正常，可以开始测试"
        return 0
    else
        log_error "部分服务未运行，请检查服务状态"
        return 1
    fi
}

# 运行单个服务测试
run_service_test() {
    local service=$1
    local test_script="$SCRIPT_DIR/test-$service.sh"
    local result_file="$TEST_RESULTS_DIR/test-$service-$TIMESTAMP.log"
    
    log_section "开始测试 $service..."
    echo ""
    
    if [ ! -f "$test_script" ]; then
        log_error "测试脚本不存在: $test_script"
        return 1
    fi
    
    # 确保脚本可执行
    chmod +x "$test_script"
    
    # 运行测试并保存结果
    if "$test_script" 2>&1 | tee "$result_file"; then
        log_success "$service 测试完成，结果保存到: $result_file"
        return 0
    else
        log_error "$service 测试失败，详细信息请查看: $result_file"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    local report_file="$TEST_RESULTS_DIR/test-report-$TIMESTAMP.md"
    
    log_info "生成测试报告..."
    
    cat > "$report_file" << EOF
# 云原生电商平台微服务测试报告

**测试时间**: $(date)
**测试环境**: 本地开发环境

## 测试概览

本次测试覆盖了以下微服务：
- user-service (用户服务)
- product-service (商品服务)  
- order-service (订单服务)

## 测试结果

EOF

    # 检查每个服务的测试结果
    for service in "${!SERVICES[@]}"; do
        local result_file="$TEST_RESULTS_DIR/test-$service-$TIMESTAMP.log"
        
        echo "### $service" >> "$report_file"
        echo "" >> "$report_file"
        
        if [ -f "$result_file" ]; then
            if grep -q "所有测试用例执行完成" "$result_file"; then
                echo "✅ **状态**: 测试通过" >> "$report_file"
            else
                echo "❌ **状态**: 测试失败" >> "$report_file"
            fi
            
            echo "📄 **详细日志**: [test-$service-$TIMESTAMP.log](./test-$service-$TIMESTAMP.log)" >> "$report_file"
        else
            echo "⚠️ **状态**: 未执行测试" >> "$report_file"
        fi
        
        echo "" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## 测试用例说明

### user-service 测试用例
- 健康检查
- 用户注册
- 用户登录
- 获取用户信息
- 检查用户名可用性
- 更新用户资料
- 用户地址管理

### product-service 测试用例
- 健康检查
- 商品分类管理
- 商品CRUD操作
- 商品搜索
- 库存管理
- 特色商品查询

### order-service 测试用例
- 健康检查
- 购物车管理
- 订单创建
- 订单查询
- 订单支付
- 订单统计

## 注意事项

1. 测试使用随机生成的测试数据，不会影响生产数据
2. 测试过程中会创建临时用户、商品和订单数据
3. 建议在独立的测试环境中运行
4. 如有测试失败，请检查服务日志和数据库连接

EOF

    log_success "测试报告生成完成: $report_file"
}

# 清理测试数据（可选）
cleanup_test_data() {
    log_info "清理测试数据..."
    
    # 这里可以添加清理逻辑，比如删除测试用户、商品等
    # 由于我们使用随机数据，通常不需要特别清理
    
    log_success "测试数据清理完成"
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -s, --service SERVICE   只测试指定的服务 (user-service|product-service|order-service)"
    echo "  -c, --check-only        只检查服务状态，不运行测试"
    echo "  --no-cleanup            测试完成后不清理测试数据"
    echo ""
    echo "示例:"
    echo "  $0                      # 测试所有服务"
    echo "  $0 -s user-service      # 只测试用户服务"
    echo "  $0 -c                   # 只检查服务状态"
}

# 主函数
main() {
    local target_service=""
    local check_only=false
    local no_cleanup=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--service)
                target_service="$2"
                shift 2
                ;;
            -c|--check-only)
                check_only=true
                shift
                ;;
            --no-cleanup)
                no_cleanup=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示测试开始信息
    echo "=========================================="
    echo "      云原生电商平台微服务测试套件"
    echo "=========================================="
    echo ""
    
    # 创建结果目录
    create_results_dir
    echo ""
    
    # 检查服务状态
    if ! check_all_services; then
        exit 1
    fi
    
    # 如果只是检查服务状态，则退出
    if [ "$check_only" = true ]; then
        log_success "服务状态检查完成"
        exit 0
    fi
    
    echo ""
    log_header "开始执行测试..."
    echo ""
    
    # 记录测试开始时间
    local start_time=$(date +%s)
    local failed_tests=0
    
    # 运行测试
    if [ -n "$target_service" ]; then
        # 测试指定服务
        if [[ " ${!SERVICES[@]} " =~ " $target_service " ]]; then
            if ! run_service_test "$target_service"; then
                ((failed_tests++))
            fi
        else
            log_error "未知服务: $target_service"
            log_info "可用服务: ${!SERVICES[*]}"
            exit 1
        fi
    else
        # 测试所有服务
        for service in user-service product-service order-service; do
            echo ""
            if ! run_service_test "$service"; then
                ((failed_tests++))
            fi
            echo ""
        done
    fi
    
    # 计算测试耗时
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    echo "=========================================="
    
    if [ $failed_tests -eq 0 ]; then
        log_success "所有测试执行完成！"
    else
        log_warning "测试完成，但有 $failed_tests 个服务测试失败"
    fi
    
    log_info "总耗时: ${duration}秒"
    echo "=========================================="
    echo ""
    
    # 生成测试报告
    if [ -z "$target_service" ]; then
        generate_test_report
        echo ""
    fi
    
    # 清理测试数据
    if [ "$no_cleanup" != true ]; then
        cleanup_test_data
        echo ""
    fi
    
    log_success "测试流程完成！"
    
    # 返回适当的退出码
    exit $failed_tests
}

# 执行主函数
main "$@"
