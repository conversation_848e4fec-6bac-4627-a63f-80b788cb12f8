#!/bin/bash

# 调试健康检查URL的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试健康检查URL
test_health_url() {
    local service_name=$1
    local url=$2
    
    log_info "测试 $service_name 健康检查: $url"
    
    # 测试连接
    if curl -s -f "$url" > /dev/null 2>&1; then
        log_success "$service_name 健康检查成功"
        
        # 获取响应内容
        response=$(curl -s "$url" 2>/dev/null || echo "无法获取响应")
        echo "响应: $response"
    else
        log_error "$service_name 健康检查失败"
        
        # 尝试获取错误信息
        error_response=$(curl -s "$url" 2>&1 || echo "连接失败")
        echo "错误信息: $error_response"
    fi
    echo ""
}

# 检查端口是否开放
check_port() {
    local port=$1
    local service_name=$2
    
    log_info "检查端口 $port ($service_name)"
    
    if netstat -an 2>/dev/null | grep -q ":$port "; then
        log_success "端口 $port 正在监听"
    else
        log_warning "端口 $port 未在监听"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "         健康检查URL调试工具"
    echo "=========================================="
    echo ""
    
    # 检查端口状态
    log_info "检查服务端口状态..."
    check_port 8081 "user-service"
    check_port 8082 "product-service"
    check_port 8083 "order-service"
    echo ""
    
    # 测试各种可能的健康检查URL
    log_info "测试健康检查URL..."
    echo ""
    
    # User Service
    test_health_url "User Service (直接)" "http://localhost:8081/health"
    test_health_url "User Service (带前缀)" "http://localhost:8081/api/users/health"
    
    # Product Service
    test_health_url "Product Service (直接)" "http://localhost:8082/health"
    test_health_url "Product Service (带前缀)" "http://localhost:8082/api/products/health"
    
    # Order Service
    test_health_url "Order Service (直接)" "http://localhost:8083/health"
    test_health_url "Order Service (带前缀)" "http://localhost:8083/api/orders/health"
    
    echo "=========================================="
    log_info "调试完成！"
    echo "=========================================="
}

# 执行主函数
main "$@"
