#!/bin/bash

# 基础健康检查测试脚本
# 用于验证所有服务的基本连通性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试健康检查
test_health() {
    local service_name=$1
    local url=$2
    
    log_info "测试 $service_name 健康检查..."
    
    response=$(curl -s -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "$service_name 健康检查通过"
        echo "响应: $response_body"
        return 0
    else
        log_error "$service_name 健康检查失败，HTTP状态码: $http_code"
        if [ "$http_code" != "000" ]; then
            echo "响应: $response_body"
        else
            echo "无法连接到服务"
        fi
        return 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "         基础健康检查测试"
    echo "=========================================="
    echo ""
    
    local failed_count=0
    
    # 测试用户服务
    if ! test_health "User Service" "http://localhost:8081/api/users/health"; then
        ((failed_count++))
    fi
    echo ""
    
    # 测试商品服务
    if ! test_health "Product Service" "http://localhost:8082/api/products/health"; then
        ((failed_count++))
    fi
    echo ""
    
    # 测试订单服务
    if ! test_health "Order Service" "http://localhost:8083/api/orders/health"; then
        ((failed_count++))
    fi
    echo ""
    
    echo "=========================================="
    if [ $failed_count -eq 0 ]; then
        log_success "所有服务健康检查通过！"
    else
        log_error "$failed_count 个服务健康检查失败"
    fi
    echo "=========================================="
    
    return $failed_count
}

# 执行主函数
main "$@"
