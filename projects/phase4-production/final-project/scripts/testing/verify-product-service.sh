#!/bin/bash

# Product Service 验证脚本
# 验证product-service编译和启动是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PRODUCT_SERVICE_DIR="../../applications/backend/product-service"
PRODUCT_SERVICE_URL="http://localhost:8082/api/products"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证编译
verify_compilation() {
    log_info "验证product-service编译..."
    
    cd "$PRODUCT_SERVICE_DIR"
    
    if mvn compile -q; then
        log_success "product-service编译成功"
        return 0
    else
        log_error "product-service编译失败"
        return 1
    fi
}

# 验证启动
verify_startup() {
    log_info "验证product-service启动..."
    
    cd "$PRODUCT_SERVICE_DIR"
    
    # 后台启动服务
    nohup mvn spring-boot:run > /dev/null 2>&1 &
    PRODUCT_SERVICE_PID=$!
    
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务是否启动成功
    for i in {1..10}; do
        if curl -s "$PRODUCT_SERVICE_URL/health" > /dev/null 2>&1; then
            log_success "product-service启动成功"
            
            # 测试健康检查
            response=$(curl -s "$PRODUCT_SERVICE_URL/health")
            if echo "$response" | grep -q '"success":true'; then
                log_success "健康检查通过"
                echo "响应: $response"
            else
                log_warning "健康检查异常"
                echo "响应: $response"
            fi
            
            # 停止服务
            pkill -f "product-service" || true
            sleep 2
            log_info "服务已停止"
            
            return 0
        fi
        log_info "等待服务启动... ($i/10)"
        sleep 3
    done
    
    log_error "product-service启动失败"
    pkill -f "product-service" || true
    return 1
}

# 主验证函数
main() {
    log_info "开始验证product-service..."
    echo "=================================="
    
    # 验证编译
    if ! verify_compilation; then
        log_error "编译验证失败，退出"
        exit 1
    fi
    
    echo ""
    
    # 验证启动
    if ! verify_startup; then
        log_error "启动验证失败，退出"
        exit 1
    fi
    
    echo ""
    log_success "product-service验证完成！所有测试通过。"
    echo "=================================="
}

# 执行主函数
main "$@"
