#!/bin/bash

# 测试脚本验证工具
# 检查所有测试脚本的语法和基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证脚本语法
validate_syntax() {
    local script=$1
    log_info "验证脚本语法: $script"
    
    if bash -n "$script"; then
        log_success "语法检查通过: $script"
        return 0
    else
        log_error "语法错误: $script"
        return 1
    fi
}

# 检查脚本权限
check_permissions() {
    local script=$1
    log_info "检查脚本权限: $script"
    
    if [ -x "$script" ]; then
        log_success "权限正确: $script"
        return 0
    else
        log_warning "脚本不可执行: $script"
        chmod +x "$script"
        log_success "已修复权限: $script"
        return 0
    fi
}

# 检查脚本依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    # 检查grep
    if ! command -v grep &> /dev/null; then
        missing_deps+=("grep")
    fi
    
    # 检查date
    if ! command -v date &> /dev/null; then
        missing_deps+=("date")
    fi
    
    if [ ${#missing_deps[@]} -eq 0 ]; then
        log_success "所有依赖都已安装"
        return 0
    else
        log_error "缺少依赖: ${missing_deps[*]}"
        return 1
    fi
}

# 验证脚本内容
validate_content() {
    local script=$1
    log_info "验证脚本内容: $script"
    
    local issues=()
    
    # 检查是否有shebang
    if ! head -1 "$script" | grep -q "^#!/bin/bash"; then
        issues+=("缺少shebang")
    fi
    
    # 检查是否有set -e
    if ! grep -q "set -e" "$script"; then
        issues+=("缺少set -e")
    fi
    
    # 检查是否有日志函数
    if ! grep -q "log_info\|log_success\|log_error" "$script"; then
        issues+=("缺少日志函数")
    fi
    
    if [ ${#issues[@]} -eq 0 ]; then
        log_success "内容检查通过: $script"
        return 0
    else
        log_warning "内容问题: $script - ${issues[*]}"
        return 1
    fi
}

# 主验证函数
main() {
    echo "=========================================="
    echo "         测试脚本验证工具"
    echo "=========================================="
    echo ""
    
    # 检查系统依赖
    if ! check_dependencies; then
        log_error "系统依赖检查失败"
        exit 1
    fi
    echo ""
    
    # 获取所有测试脚本
    local scripts=(
        "test-user-service.sh"
        "test-product-service.sh"
        "test-order-service.sh"
        "test-all-services.sh"
        "demo-test.sh"
        "health-check.sh"
    )
    
    local total_scripts=${#scripts[@]}
    local passed_scripts=0
    local failed_scripts=0
    
    log_info "开始验证 $total_scripts 个脚本..."
    echo ""
    
    # 验证每个脚本
    for script in "${scripts[@]}"; do
        if [ ! -f "$script" ]; then
            log_error "脚本不存在: $script"
            ((failed_scripts++))
            continue
        fi
        
        echo "验证脚本: $script"
        echo "----------------------------------------"
        
        local script_passed=true
        
        # 检查权限
        if ! check_permissions "$script"; then
            script_passed=false
        fi
        
        # 验证语法
        if ! validate_syntax "$script"; then
            script_passed=false
        fi
        
        # 验证内容
        if ! validate_content "$script"; then
            # 内容问题不算致命错误，只是警告
            :
        fi
        
        if [ "$script_passed" = true ]; then
            log_success "脚本验证通过: $script"
            ((passed_scripts++))
        else
            log_error "脚本验证失败: $script"
            ((failed_scripts++))
        fi
        
        echo ""
    done
    
    # 显示验证结果
    echo "=========================================="
    echo "验证结果汇总:"
    echo "  总脚本数: $total_scripts"
    echo "  通过验证: $passed_scripts"
    echo "  验证失败: $failed_scripts"
    echo "=========================================="
    
    if [ $failed_scripts -eq 0 ]; then
        log_success "所有脚本验证通过！"
        echo ""
        log_info "可以安全使用测试脚本了"
        return 0
    else
        log_error "有 $failed_scripts 个脚本验证失败"
        echo ""
        log_info "请修复上述问题后重新验证"
        return 1
    fi
}

# 执行主函数
main "$@"
