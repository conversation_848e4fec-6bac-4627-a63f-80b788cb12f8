#!/bin/bash

# 商品服务测试脚本
# 测试商品服务的核心功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8082/api/products"
USER_SERVICE_URL="http://localhost:8081/api/users"
TEST_USER_USERNAME="testuser$(date +%s)"
TEST_USER_EMAIL="test$(date +%s)@example.com"
TEST_USER_PASSWORD="Password123"
ACCESS_TOKEN=""
CATEGORY_ID=""
PRODUCT_ID=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否启动
check_service() {
    log_info "检查商品服务是否启动..."
    
    for i in {1..30}; do
        if curl -s -f "$BASE_URL/health" > /dev/null 2>&1; then
            log_success "商品服务已启动"
            return 0
        fi
        log_info "等待服务启动... ($i/30)"
        sleep 2
    done
    
    log_error "商品服务启动超时"
    return 1
}

# 获取管理员访问令牌
get_admin_token() {
    log_info "获取管理员访问令牌..."
    
    # 先注册测试用户
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$TEST_USER_USERNAME\",
            \"email\": \"$TEST_USER_EMAIL\",
            \"password\": \"$TEST_USER_PASSWORD\",
            \"confirmPassword\": \"$TEST_USER_PASSWORD\"
        }" \
        "$USER_SERVICE_URL/auth/register" > /dev/null
    
    # 登录获取令牌
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{
            \"usernameOrEmail\": \"$TEST_USER_USERNAME\",
            \"password\": \"$TEST_USER_PASSWORD\"
        }" \
        "$USER_SERVICE_URL/auth/login")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        ACCESS_TOKEN=$(echo "$response_body" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$ACCESS_TOKEN" ]; then
            log_success "获取到访问令牌: ${ACCESS_TOKEN:0:20}..."
        else
            log_warning "未能提取访问令牌"
        fi
    else
        log_error "获取访问令牌失败"
        return 1
    fi
}

# 测试健康检查
test_health_check() {
    log_info "测试健康检查接口..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/health")
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        log_success "健康检查通过"
        echo "响应: ${response%???}"
    else
        log_error "健康检查失败，HTTP状态码: $http_code"
        return 1
    fi
}

# 测试创建分类
test_create_category() {
    log_info "测试创建商品分类..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "没有访问令牌，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{
            \"name\": \"测试分类$(date +%s)\",
            \"description\": \"这是一个测试分类\",
            \"sortOrder\": 1,
            \"isActive\": true
        }" \
        "$BASE_URL/categories")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "201" ]; then
        log_success "创建分类成功"
        # 提取分类ID
        CATEGORY_ID=$(echo "$response_body" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
        if [ -n "$CATEGORY_ID" ]; then
            log_success "获取到分类ID: $CATEGORY_ID"
        fi
        echo "响应: $response_body"
    else
        log_error "创建分类失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取分类列表
test_get_categories() {
    log_info "测试获取分类列表..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/categories")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取分类列表成功"
        echo "响应: $response_body"
    else
        log_error "获取分类列表失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试创建商品
test_create_product() {
    log_info "测试创建商品..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$CATEGORY_ID" ]; then
        log_error "没有访问令牌或分类ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "{
            \"name\": \"测试商品$(date +%s)\",
            \"sku\": \"TEST-SKU-$(date +%s)\",
            \"description\": \"这是一个测试商品\",
            \"price\": 99.99,
            \"originalPrice\": 129.99,
            \"stockQuantity\": 100,
            \"lowStockThreshold\": 10,
            \"brand\": \"测试品牌\",
            \"weight\": 1.5,
            \"categoryId\": $CATEGORY_ID,
            \"status\": \"ACTIVE\",
            \"isFeatured\": true
        }" \
        "$BASE_URL/")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "201" ]; then
        log_success "创建商品成功"
        # 提取商品ID
        PRODUCT_ID=$(echo "$response_body" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
        if [ -n "$PRODUCT_ID" ]; then
            log_success "获取到商品ID: $PRODUCT_ID"
        fi
        echo "响应: $response_body"
    else
        log_error "创建商品失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取商品列表
test_get_products() {
    log_info "测试获取商品列表..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/?page=0&size=10")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取商品列表成功"
        echo "响应: $response_body"
    else
        log_error "获取商品列表失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试根据ID获取商品
test_get_product_by_id() {
    log_info "测试根据ID获取商品..."
    
    if [ -z "$PRODUCT_ID" ]; then
        log_error "没有商品ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/$PRODUCT_ID")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取商品详情成功"
        echo "响应: $response_body"
    else
        log_error "获取商品详情失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试搜索商品
test_search_products() {
    log_info "测试搜索商品..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/search?keyword=测试")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "搜索商品成功"
        echo "响应: $response_body"
    else
        log_error "搜索商品失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试根据分类获取商品
test_get_products_by_category() {
    log_info "测试根据分类获取商品..."
    
    if [ -z "$CATEGORY_ID" ]; then
        log_error "没有分类ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/category/$CATEGORY_ID")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "根据分类获取商品成功"
        echo "响应: $response_body"
    else
        log_error "根据分类获取商品失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试获取特色商品
test_get_featured_products() {
    log_info "测试获取特色商品..."
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL/featured")
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "获取特色商品成功"
        echo "响应: $response_body"
    else
        log_error "获取特色商品失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试更新商品库存
test_update_stock() {
    log_info "测试更新商品库存..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$PRODUCT_ID" ]; then
        log_error "没有访问令牌或商品ID，跳过测试"
        return 1
    fi
    
    response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/$PRODUCT_ID/stock?quantity=150")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "更新商品库存成功"
        echo "响应: $response_body"
    else
        log_error "更新商品库存失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 测试库存操作
test_inventory_operations() {
    log_info "测试库存操作..."
    
    if [ -z "$ACCESS_TOKEN" ] || [ -z "$PRODUCT_ID" ]; then
        log_error "没有访问令牌或商品ID，跳过测试"
        return 1
    fi
    
    # 测试采购入库
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "$BASE_URL/$PRODUCT_ID/inventory/purchase?quantity=50&remarks=测试采购入库")
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "采购入库操作成功"
        echo "响应: $response_body"
    else
        log_error "采购入库操作失败，HTTP状态码: $http_code"
        echo "响应: $response_body"
        return 1
    fi
}

# 主测试函数
main() {
    echo "=========================================="
    echo "         商品服务功能测试"
    echo "=========================================="
    echo ""
    
    # 检查服务状态
    if ! check_service; then
        log_error "服务未启动，请先启动商品服务"
        exit 1
    fi
    
    # 获取访问令牌
    if ! get_admin_token; then
        log_error "获取访问令牌失败"
        exit 1
    fi
    
    echo ""
    echo "开始执行测试用例..."
    echo ""
    
    # 执行测试用例
    test_health_check
    echo ""
    
    test_create_category
    echo ""
    
    test_get_categories
    echo ""
    
    test_create_product
    echo ""
    
    test_get_products
    echo ""
    
    test_get_product_by_id
    echo ""
    
    test_search_products
    echo ""
    
    test_get_products_by_category
    echo ""
    
    test_get_featured_products
    echo ""
    
    test_update_stock
    echo ""
    
    test_inventory_operations
    echo ""
    
    echo "=========================================="
    log_success "所有测试用例执行完成！"
    echo "=========================================="
}

# 执行主函数
main "$@"
