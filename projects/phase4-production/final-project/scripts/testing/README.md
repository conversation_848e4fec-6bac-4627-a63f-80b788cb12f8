# 微服务测试脚本使用指南

本目录包含了云原生电商平台所有微服务的自动化测试脚本。

## 📁 文件结构

```
scripts/testing/
├── README.md                    # 本文件
├── test-all-services.sh         # 综合测试脚本
├── test-user-service.sh         # 用户服务测试
├── test-product-service.sh      # 商品服务测试
├── test-order-service.sh        # 订单服务测试
└── results/                     # 测试结果目录（自动创建）
```

## 🚀 快速开始

### 1. 启动所有服务

在运行测试之前，请确保所有微服务都已启动：

```bash
# 启动用户服务
cd applications/backend/user-service
mvn spring-boot:run

# 启动商品服务  
cd applications/backend/product-service
mvn spring-boot:run

# 启动订单服务
cd applications/backend/order-service
mvn spring-boot:run
```

### 2. 运行测试

#### 测试所有服务
```bash
./scripts/testing/test-all-services.sh
```

#### 测试单个服务
```bash
# 测试用户服务
./scripts/testing/test-user-service.sh

# 测试商品服务
./scripts/testing/test-product-service.sh

# 测试订单服务
./scripts/testing/test-order-service.sh
```

#### 只检查服务状态
```bash
./scripts/testing/test-all-services.sh --check-only
```

#### 测试指定服务
```bash
./scripts/testing/test-all-services.sh --service user-service
```

## 📋 测试用例说明

### User Service 测试用例
- ✅ 健康检查接口
- ✅ 用户注册功能
- ✅ 用户登录功能
- ✅ 获取当前用户信息
- ✅ 检查用户名可用性
- ✅ 更新用户详细信息
- ✅ 添加用户地址
- ✅ 获取用户地址列表

### Product Service 测试用例
- ✅ 健康检查接口
- ✅ 创建商品分类
- ✅ 获取分类列表
- ✅ 创建商品
- ✅ 获取商品列表
- ✅ 根据ID获取商品
- ✅ 搜索商品
- ✅ 根据分类获取商品
- ✅ 获取特色商品
- ✅ 更新商品库存
- ✅ 库存操作（采购入库）

### Order Service 测试用例
- ✅ 健康检查接口
- ✅ 获取用户购物车
- ✅ 添加商品到购物车
- ✅ 更新购物车商品数量
- ✅ 获取购物车商品数量
- ✅ 从购物车创建订单
- ✅ 获取用户订单列表
- ✅ 根据ID获取订单详情
- ✅ 模拟支付订单
- ✅ 检查订单取消状态
- ✅ 获取用户订单统计
- ✅ 从购物车移除商品

## 🔧 配置说明

### 服务端口配置
- User Service: `http://localhost:8081`
- Product Service: `http://localhost:8082`
- Order Service: `http://localhost:8083`

### 测试数据
- 所有测试使用随机生成的测试数据
- 测试用户名格式: `testuser{timestamp}`
- 测试邮箱格式: `test{timestamp}@example.com`
- 测试密码: `Password123`

## 📊 测试结果

### 结果文件
测试结果会保存在 `results/` 目录下：
- `test-{service}-{timestamp}.log` - 单个服务的详细测试日志
- `test-report-{timestamp}.md` - 综合测试报告（Markdown格式）

### 测试报告示例
```
# 云原生电商平台微服务测试报告

**测试时间**: 2024-01-01 12:00:00
**测试环境**: 本地开发环境

## 测试结果

### user-service
✅ **状态**: 测试通过
📄 **详细日志**: test-user-service-20240101_120000.log

### product-service  
✅ **状态**: 测试通过
📄 **详细日志**: test-product-service-20240101_120000.log

### order-service
✅ **状态**: 测试通过
📄 **详细日志**: test-order-service-20240101_120000.log
```

## 🛠️ 高级用法

### 命令行选项
```bash
# 显示帮助信息
./test-all-services.sh --help

# 只测试指定服务
./test-all-services.sh --service user-service

# 只检查服务状态
./test-all-services.sh --check-only

# 测试完成后不清理数据
./test-all-services.sh --no-cleanup
```

### 自定义配置
如需修改服务地址或端口，请编辑对应测试脚本中的配置变量：

```bash
# 在 test-user-service.sh 中
BASE_URL="http://localhost:8081/api/users"

# 在 test-product-service.sh 中  
BASE_URL="http://localhost:8082/api/products"

# 在 test-order-service.sh 中
BASE_URL="http://localhost:8083/api/orders"
```

## 🐛 故障排除

### 常见问题

1. **服务未启动**
   ```
   [ERROR] 服务未启动，请先启动XXX服务
   ```
   解决方案：确保对应的微服务已启动并监听正确端口

2. **访问令牌获取失败**
   ```
   [ERROR] 获取访问令牌失败
   ```
   解决方案：检查用户服务是否正常运行，数据库连接是否正常

3. **测试数据冲突**
   ```
   [ERROR] 用户注册失败，HTTP状态码: 409
   ```
   解决方案：测试脚本使用时间戳生成唯一数据，通常不会冲突

4. **网络连接问题**
   ```
   [ERROR] curl: (7) Failed to connect to localhost port 8081
   ```
   解决方案：检查服务是否在指定端口运行，防火墙设置是否正确

### 调试技巧

1. **查看详细日志**
   ```bash
   # 运行单个服务测试并查看详细输出
   ./test-user-service.sh
   ```

2. **手动测试API**
   ```bash
   # 测试健康检查
   curl http://localhost:8081/api/users/health
   
   # 测试用户注册
   curl -X POST -H "Content-Type: application/json" \
        -d '{"username":"test","email":"<EMAIL>","password":"Password123","confirmPassword":"Password123"}' \
        http://localhost:8081/api/users/auth/register
   ```

3. **检查服务日志**
   查看各个微服务的控制台输出，了解详细的错误信息

## 📝 贡献指南

如需添加新的测试用例或修改现有测试：

1. 遵循现有的脚本结构和命名规范
2. 添加适当的日志输出和错误处理
3. 更新本README文件中的测试用例说明
4. 确保测试数据的唯一性，避免冲突

## 📞 支持

如有问题或建议，请：
1. 检查本文档的故障排除部分
2. 查看测试日志文件获取详细错误信息
3. 联系开发团队获取支持
