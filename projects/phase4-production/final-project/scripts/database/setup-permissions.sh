#!/bin/bash

# =====================================================
# 云原生电商平台 - 数据库权限设置脚本
# =====================================================
# 
# 用途：自动为ecommerce用户设置数据库权限
# 作者：ecommerce-platform
# 版本：1.0.0
# 
# =====================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_FILE="${SCRIPT_DIR}/grant-permissions.sql"

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="3306"
DEFAULT_ROOT_USER="root"
DEFAULT_CONTAINER_NAME="ecommerce-mariadb"

# 显示帮助信息
show_help() {
    cat << EOF
云原生电商平台 - 数据库权限设置脚本

用法:
    $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -H, --host HOST         数据库主机 (默认: $DEFAULT_HOST)
    -P, --port PORT         数据库端口 (默认: $DEFAULT_PORT)
    -u, --user USER         root用户名 (默认: $DEFAULT_ROOT_USER)
    -p, --password PASS     root密码 (如果不提供，将提示输入)
    -c, --container NAME    Docker容器名称 (默认: $DEFAULT_CONTAINER_NAME)
    -d, --docker            使用Docker容器连接
    -v, --verify            仅验证权限，不执行赋权操作

示例:
    # 使用Docker容器连接
    $0 --docker

    # 直接连接数据库
    $0 --host localhost --user root --password mypassword

    # 仅验证权限
    $0 --docker --verify

EOF
}

# 解析命令行参数
HOST="$DEFAULT_HOST"
PORT="$DEFAULT_PORT"
USER="$DEFAULT_ROOT_USER"
PASSWORD=""
CONTAINER_NAME="$DEFAULT_CONTAINER_NAME"
USE_DOCKER=false
VERIFY_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -H|--host)
            HOST="$2"
            shift 2
            ;;
        -P|--port)
            PORT="$2"
            shift 2
            ;;
        -u|--user)
            USER="$2"
            shift 2
            ;;
        -p|--password)
            PASSWORD="$2"
            shift 2
            ;;
        -c|--container)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -d|--docker)
            USE_DOCKER=true
            shift
            ;;
        -v|--verify)
            VERIFY_ONLY=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查SQL文件是否存在
if [[ ! -f "$SQL_FILE" ]]; then
    log_error "SQL文件不存在: $SQL_FILE"
    exit 1
fi

# 验证权限函数
verify_permissions() {
    local cmd="$1"
    
    log_info "验证ecommerce用户权限..."
    
    # 执行权限检查
    if eval "$cmd -e \"SHOW GRANTS FOR 'ecommerce'@'%';\"" 2>/dev/null; then
        log_success "ecommerce用户权限验证成功"
        
        # 显示具体权限
        log_info "当前权限详情:"
        eval "$cmd -e \"SHOW GRANTS FOR 'ecommerce'@'%';\""
        
        return 0
    else
        log_warning "ecommerce用户可能不存在或权限不足"
        return 1
    fi
}

# 执行权限赋予函数
grant_permissions() {
    local cmd="$1"
    
    log_info "开始执行权限赋予..."
    
    if eval "$cmd < \"$SQL_FILE\""; then
        log_success "权限赋予脚本执行成功"
        return 0
    else
        log_error "权限赋予脚本执行失败"
        return 1
    fi
}

# 主执行逻辑
main() {
    log_info "云原生电商平台 - 数据库权限设置"
    log_info "=================================="
    
    # 构建数据库连接命令
    if [[ "$USE_DOCKER" == true ]]; then
        log_info "使用Docker容器连接: $CONTAINER_NAME"
        
        # 检查容器是否运行
        if ! docker ps | grep -q "$CONTAINER_NAME"; then
            log_error "Docker容器 '$CONTAINER_NAME' 未运行"
            log_info "请先启动MariaDB容器"
            exit 1
        fi
        
        # Docker连接命令
        if [[ -n "$PASSWORD" ]]; then
            DB_CMD="docker exec -i $CONTAINER_NAME mysql -u$USER -p$PASSWORD"
        else
            DB_CMD="docker exec -i $CONTAINER_NAME mysql -u$USER -prootpassword"
        fi
    else
        log_info "直接连接数据库: $HOST:$PORT"
        
        # 如果没有提供密码，提示输入
        if [[ -z "$PASSWORD" ]]; then
            read -s -p "请输入root密码: " PASSWORD
            echo
        fi
        
        # 直接连接命令
        DB_CMD="mysql -h$HOST -P$PORT -u$USER -p$PASSWORD"
    fi
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    if eval "$DB_CMD -e 'SELECT 1;'" >/dev/null 2>&1; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败，请检查连接参数"
        exit 1
    fi
    
    # 根据模式执行操作
    if [[ "$VERIFY_ONLY" == true ]]; then
        # 仅验证模式
        verify_permissions "$DB_CMD"
    else
        # 执行权限赋予
        if grant_permissions "$DB_CMD"; then
            log_info "等待2秒后验证权限..."
            sleep 2
            verify_permissions "$DB_CMD"
        else
            exit 1
        fi
    fi
    
    log_success "数据库权限设置完成！"
    log_info "ecommerce用户现在可以访问所有微服务数据库"
}

# 执行主函数
main "$@"
