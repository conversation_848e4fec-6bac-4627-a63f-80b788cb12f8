-- =====================================================
-- 云原生电商平台 - 数据库权限赋予脚本
-- =====================================================
-- 
-- 用途：为ecommerce用户赋予所有必要数据库的权限
-- 使用方法：
--   1. 使用root用户连接到MariaDB
--   2. 执行此脚本：SOURCE /path/to/grant-permissions.sql;
--   3. 或者通过命令行：mysql -u root -p < grant-permissions.sql
-- 
-- =====================================================

-- 确保所有必要的数据库存在
CREATE DATABASE IF NOT EXISTS ecommerce CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_user CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_product CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_order CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_notification CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_recommendation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- =====================================================
-- 用户管理
-- =====================================================

-- 创建ecommerce用户（如果不存在）
-- 注意：在生产环境中应使用更强的密码
CREATE USER IF NOT EXISTS 'ecommerce'@'%' IDENTIFIED BY 'password';
CREATE USER IF NOT EXISTS 'ecommerce'@'localhost' IDENTIFIED BY 'password';

-- =====================================================
-- 权限赋予
-- =====================================================

-- 为ecommerce用户赋予所有数据库的完整权限
-- 主数据库权限
GRANT ALL PRIVILEGES ON ecommerce.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce.* TO 'ecommerce'@'localhost';

-- 用户服务数据库权限
GRANT ALL PRIVILEGES ON ecommerce_user.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_user.* TO 'ecommerce'@'localhost';

-- 商品服务数据库权限
GRANT ALL PRIVILEGES ON ecommerce_product.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_product.* TO 'ecommerce'@'localhost';

-- 订单服务数据库权限
GRANT ALL PRIVILEGES ON ecommerce_order.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_order.* TO 'ecommerce'@'localhost';

-- 通知服务数据库权限
GRANT ALL PRIVILEGES ON ecommerce_notification.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_notification.* TO 'ecommerce'@'localhost';

-- 推荐服务数据库权限
GRANT ALL PRIVILEGES ON ecommerce_recommendation.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_recommendation.* TO 'ecommerce'@'localhost';

-- =====================================================
-- 应用权限并验证
-- =====================================================

-- 刷新权限表
FLUSH PRIVILEGES;

-- 显示用户权限（验证）
SELECT '=== ecommerce@% 用户权限 ===' AS info;
SHOW GRANTS FOR 'ecommerce'@'%';

SELECT '=== ecommerce@localhost 用户权限 ===' AS info;
SHOW GRANTS FOR 'ecommerce'@'localhost';

-- 显示所有数据库
SELECT '=== 所有数据库列表 ===' AS info;
SHOW DATABASES;

-- 权限赋予完成提示
SELECT 
    'SUCCESS: 数据库权限赋予完成！' AS status,
    'ecommerce用户现在可以访问所有微服务数据库' AS message,
    NOW() AS completed_at;

-- =====================================================
-- 安全建议
-- =====================================================
/*
生产环境安全建议：

1. 修改默认密码：
   ALTER USER 'ecommerce'@'%' IDENTIFIED BY 'your_strong_password_here';

2. 限制访问来源（如果可能）：
   - 将 '%' 替换为具体的IP地址或网段
   - 例如：'ecommerce'@'10.0.0.%' 或 'ecommerce'@'*************'

3. 使用最小权限原则：
   - 根据实际需要，可以将 ALL PRIVILEGES 替换为具体权限
   - 例如：GRANT SELECT, INSERT, UPDATE, DELETE ON database.* TO 'user'@'host';

4. 定期审查权限：
   - 定期检查用户权限：SHOW GRANTS FOR 'ecommerce'@'%';
   - 删除不需要的用户：DROP USER 'username'@'host';

5. 启用审计日志：
   - 在MariaDB配置中启用审计插件
   - 监控数据库访问和操作

6. 使用SSL连接：
   - 配置SSL证书
   - 强制SSL连接：GRANT ... REQUIRE SSL;
*/
