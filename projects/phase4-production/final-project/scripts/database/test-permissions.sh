#!/bin/bash

# =====================================================
# 云原生电商平台 - 数据库权限测试脚本
# =====================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
CONTAINER_NAME="ecommerce-mariadb"
DB_USER="ecommerce"
DB_PASSWORD="password"

# 数据库列表
DATABASES=(
    "ecommerce"
    "ecommerce_user"
    "ecommerce_product"
    "ecommerce_order"
    "ecommerce_notification"
    "ecommerce_recommendation"
)

log_info "云原生电商平台 - 数据库权限测试"
log_info "=================================="

# 检查Docker容器
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    log_error "Docker容器 '$CONTAINER_NAME' 未运行"
    exit 1
fi

log_success "Docker容器运行正常"

# 测试root连接
log_info "测试root用户连接..."
if docker exec -i "$CONTAINER_NAME" mysql -u root -prootpassword -e "SELECT 1;" >/dev/null 2>&1; then
    log_success "root用户连接成功"
else
    log_error "root用户连接失败"
    exit 1
fi

# 测试ecommerce用户连接
log_info "测试ecommerce用户连接..."
if docker exec -i "$CONTAINER_NAME" mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
    log_success "ecommerce用户连接成功"
else
    log_error "ecommerce用户连接失败"
    exit 1
fi

# 测试每个数据库的访问权限
log_info "测试数据库访问权限..."

for db in "${DATABASES[@]}"; do
    log_info "测试数据库: $db"
    
    # 测试SELECT权限
    if docker exec -i "$CONTAINER_NAME" mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $db; SELECT 1 AS test;" >/dev/null 2>&1; then
        log_success "  ✓ SELECT权限正常"
    else
        log_error "  ✗ SELECT权限失败"
        continue
    fi
    
    # 测试CREATE权限（创建临时表）
    if docker exec -i "$CONTAINER_NAME" mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $db; CREATE TABLE IF NOT EXISTS test_permissions (id INT); DROP TABLE IF EXISTS test_permissions;" >/dev/null 2>&1; then
        log_success "  ✓ CREATE/DROP权限正常"
    else
        log_error "  ✗ CREATE/DROP权限失败"
    fi
done

# 显示用户权限详情
log_info "显示ecommerce用户权限详情..."
docker exec -i "$CONTAINER_NAME" mysql -u root -prootpassword -e "SHOW GRANTS FOR '$DB_USER'@'%';"

# 显示所有数据库
log_info "显示所有数据库..."
docker exec -i "$CONTAINER_NAME" mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "SHOW DATABASES;"

log_success "数据库权限测试完成！"
log_info "所有微服务都可以正常访问对应的数据库"
