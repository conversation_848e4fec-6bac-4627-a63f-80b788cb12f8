-- =====================================================
-- 云原生电商平台 - MariaDB数据库初始化脚本
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS ecommerce CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_user CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_product CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_order CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_notification CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS ecommerce_recommendation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建ecommerce用户并赋予权限
CREATE USER IF NOT EXISTS 'ecommerce'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON ecommerce.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_user.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_product.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_order.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_notification.* TO 'ecommerce'@'%';
GRANT ALL PRIVILEGES ON ecommerce_recommendation.* TO 'ecommerce'@'%';
FLUSH PRIVILEGES;

-- =====================================================
-- 用户服务数据库表
-- =====================================================
USE ecommerce_user;

-- 用户基础信息表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    phone VARCHAR(20) COMMENT '手机号',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 用户详细信息表
CREATE TABLE user_profiles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    first_name VARCHAR(50) COMMENT '名',
    last_name VARCHAR(50) COMMENT '姓',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    birth_date DATE COMMENT '生日',
    gender ENUM('MALE', 'FEMALE', 'OTHER') COMMENT '性别',
    bio TEXT COMMENT '个人简介',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户详细信息表';

-- 用户地址表
CREATE TABLE user_addresses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detail_address VARCHAR(255) NOT NULL COMMENT '详细地址',
    postal_code VARCHAR(10) COMMENT '邮政编码',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

-- =====================================================
-- 商品服务数据库表
-- =====================================================
USE ecommerce_product;

-- 商品分类表
CREATE TABLE categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    parent_id BIGINT COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    icon_url VARCHAR(255) COMMENT '分类图标',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 商品表
CREATE TABLE products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    brand VARCHAR(100) COMMENT '品牌',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    stock_quantity INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    sold_quantity INT DEFAULT 0 COMMENT '销售数量',
    sku VARCHAR(100) UNIQUE NOT NULL COMMENT 'SKU编码',
    status ENUM('ACTIVE', 'INACTIVE', 'OUT_OF_STOCK') DEFAULT 'ACTIVE' COMMENT '状态',
    weight DECIMAL(8,2) COMMENT '重量(kg)',
    dimensions VARCHAR(100) COMMENT '尺寸',
    images JSON COMMENT '商品图片',
    attributes JSON COMMENT '商品属性',
    tags VARCHAR(500) COMMENT '标签',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    INDEX idx_category (category_id),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_price (price),
    FULLTEXT idx_name_desc (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 库存变更记录表
CREATE TABLE inventory_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT NOT NULL COMMENT '商品ID',
    change_type ENUM('IN', 'OUT', 'ADJUST') NOT NULL COMMENT '变更类型',
    quantity_change INT NOT NULL COMMENT '数量变更',
    quantity_before INT NOT NULL COMMENT '变更前数量',
    quantity_after INT NOT NULL COMMENT '变更后数量',
    reason VARCHAR(255) COMMENT '变更原因',
    operator_id BIGINT COMMENT '操作员ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_product_id (product_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存变更记录表';

-- =====================================================
-- 订单服务数据库表
-- =====================================================
USE ecommerce_order;

-- 购物车表
CREATE TABLE shopping_carts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL COMMENT '数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_product (user_id, product_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- 订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    order_number VARCHAR(50) UNIQUE NOT NULL COMMENT '订单号',
    status ENUM('PENDING', 'CONFIRMED', 'PAID', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING' COMMENT '订单状态',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    shipping_fee DECIMAL(10,2) DEFAULT 0 COMMENT '运费',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    shipping_address JSON NOT NULL COMMENT '收货地址',
    payment_method VARCHAR(50) COMMENT '支付方式',
    payment_status ENUM('PENDING', 'PAID', 'FAILED', 'REFUNDED') DEFAULT 'PENDING' COMMENT '支付状态',
    payment_time TIMESTAMP NULL COMMENT '支付时间',
    shipped_time TIMESTAMP NULL COMMENT '发货时间',
    delivered_time TIMESTAMP NULL COMMENT '收货时间',
    remark TEXT COMMENT '订单备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_order_number (order_number),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单商品表
CREATE TABLE order_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_sku VARCHAR(100) NOT NULL COMMENT '商品SKU',
    quantity INT NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    product_snapshot JSON COMMENT '商品快照',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';

-- =====================================================
-- 通知服务数据库表
-- =====================================================
USE ecommerce_notification;

-- 通知模板表
CREATE TABLE notification_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    type ENUM('EMAIL', 'SMS', 'PUSH', 'SYSTEM') NOT NULL COMMENT '通知类型',
    subject VARCHAR(255) COMMENT '主题',
    content TEXT NOT NULL COMMENT '内容模板',
    variables JSON COMMENT '变量定义',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知模板表';

-- 通知记录表
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type ENUM('EMAIL', 'SMS', 'PUSH', 'SYSTEM') NOT NULL COMMENT '通知类型',
    title VARCHAR(255) NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    status ENUM('PENDING', 'SENT', 'FAILED', 'READ') DEFAULT 'PENDING' COMMENT '状态',
    send_time TIMESTAMP NULL COMMENT '发送时间',
    read_time TIMESTAMP NULL COMMENT '阅读时间',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知记录表';

-- =====================================================
-- 推荐服务数据库表
-- =====================================================
USE ecommerce_recommendation;

-- 用户行为记录表
CREATE TABLE user_behaviors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    behavior_type ENUM('VIEW', 'CLICK', 'CART', 'PURCHASE', 'FAVORITE') NOT NULL COMMENT '行为类型',
    session_id VARCHAR(100) COMMENT '会话ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为记录表';

-- 推荐结果表
CREATE TABLE recommendations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '推荐商品ID',
    algorithm VARCHAR(50) NOT NULL COMMENT '推荐算法',
    score DECIMAL(5,4) NOT NULL COMMENT '推荐分数',
    reason VARCHAR(255) COMMENT '推荐理由',
    position INT COMMENT '推荐位置',
    status ENUM('ACTIVE', 'CLICKED', 'EXPIRED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_score (score),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐结果表';

-- =====================================================
-- 插入初始数据
-- =====================================================

-- 插入商品分类数据
USE ecommerce_product;
INSERT INTO categories (name, description, sort_order) VALUES
('电子产品', '各类电子设备和数码产品', 1),
('服装鞋帽', '男女服装、鞋子、帽子等', 2),
('家居用品', '家具、装饰品、生活用品', 3),
('图书音像', '图书、音乐、影视产品', 4),
('运动户外', '运动器材、户外用品', 5);

-- 插入子分类
INSERT INTO categories (name, description, parent_id, sort_order) VALUES
('手机通讯', '智能手机、对讲机等', 1, 1),
('电脑办公', '笔记本、台式机、办公设备', 1, 2),
('男装', '男士服装', 2, 1),
('女装', '女士服装', 2, 2),
('家具', '沙发、床、桌椅等', 3, 1);

-- 插入通知模板
USE ecommerce_notification;
INSERT INTO notification_templates (name, type, subject, content, variables) VALUES
('用户注册欢迎', 'EMAIL', '欢迎注册云原生电商平台', '亲爱的 {{username}}，欢迎您注册我们的平台！', '{"username": "用户名"}'),
('订单确认通知', 'EMAIL', '订单确认 - {{orderNumber}}', '您的订单 {{orderNumber}} 已确认，总金额：{{totalAmount}} 元', '{"orderNumber": "订单号", "totalAmount": "订单金额"}'),
('支付成功通知', 'SYSTEM', '支付成功', '订单 {{orderNumber}} 支付成功', '{"orderNumber": "订单号"}'),
('发货通知', 'SMS', '商品已发货', '您的订单 {{orderNumber}} 已发货，请注意查收', '{"orderNumber": "订单号"}');

COMMIT;
