# 数据库初始化和权限管理

本目录包含云原生电商平台的数据库初始化和权限管理脚本。

## 📁 文件说明

### 核心脚本

- **`init-mariadb.sql`** - 完整的数据库初始化脚本
  - 创建所有微服务数据库
  - 创建数据表结构
  - 插入初始数据
  - 创建用户并赋予权限

- **`grant-permissions.sql`** - 专门的权限赋予脚本
  - 创建ecommerce用户
  - 为所有数据库赋予权限
  - 包含安全建议和最佳实践

- **`setup-permissions.sh`** - 自动化权限设置脚本
  - 支持Docker和直接连接两种方式
  - 自动验证权限设置
  - 提供详细的日志输出

## 🚀 使用方法

### 方法一：使用自动化脚本（推荐）

#### 1. Docker环境（推荐）
```bash
# 验证当前权限
./setup-permissions.sh --docker --verify

# 设置权限
./setup-permissions.sh --docker

# 使用自定义容器名
./setup-permissions.sh --docker --container my-mariadb
```

#### 2. 直接连接数据库
```bash
# 交互式输入密码
./setup-permissions.sh --host localhost --user root

# 直接提供密码
./setup-permissions.sh --host localhost --user root --password mypassword

# 连接远程数据库
./setup-permissions.sh --host ************* --port 3306 --user root
```

### 方法二：手动执行SQL脚本

#### 1. 完整初始化
```bash
# 使用Docker容器
docker exec -i ecommerce-mariadb mysql -u root -prootpassword < init-mariadb.sql

# 直接连接
mysql -h localhost -u root -p < init-mariadb.sql
```

#### 2. 仅设置权限
```bash
# 使用Docker容器
docker exec -i ecommerce-mariadb mysql -u root -prootpassword < grant-permissions.sql

# 直接连接
mysql -h localhost -u root -p < grant-permissions.sql
```

### 方法三：交互式执行

```bash
# 连接到数据库
docker exec -it ecommerce-mariadb mysql -u root -prootpassword

# 在MySQL命令行中执行
mysql> SOURCE /path/to/grant-permissions.sql;
```

## 🔐 权限说明

### 创建的用户
- **用户名**: `ecommerce`
- **密码**: `password` (生产环境请修改)
- **访问范围**: `%` (所有主机) 和 `localhost`

### 赋予的权限
ecommerce用户对以下数据库拥有完整权限（ALL PRIVILEGES）：

- `ecommerce` - 主数据库
- `ecommerce_user` - 用户服务数据库
- `ecommerce_product` - 商品服务数据库  
- `ecommerce_order` - 订单服务数据库
- `ecommerce_notification` - 通知服务数据库
- `ecommerce_recommendation` - 推荐服务数据库

## 🛡️ 安全建议

### 生产环境配置

1. **修改默认密码**
```sql
ALTER USER 'ecommerce'@'%' IDENTIFIED BY 'your_strong_password_here';
```

2. **限制访问来源**
```sql
-- 删除通配符用户
DROP USER 'ecommerce'@'%';

-- 创建限制IP的用户
CREATE USER 'ecommerce'@'10.0.0.%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON ecommerce_*.* TO 'ecommerce'@'10.0.0.%';
```

3. **使用最小权限原则**
```sql
-- 替换ALL PRIVILEGES为具体权限
GRANT SELECT, INSERT, UPDATE, DELETE ON ecommerce_user.* TO 'ecommerce'@'host';
```

4. **启用SSL连接**
```sql
GRANT ALL PRIVILEGES ON ecommerce_*.* TO 'ecommerce'@'host' REQUIRE SSL;
```

## 🔍 验证和故障排除

### 验证权限设置
```bash
# 使用脚本验证
./setup-permissions.sh --docker --verify

# 手动验证
docker exec -it ecommerce-mariadb mysql -u root -prootpassword -e "SHOW GRANTS FOR 'ecommerce'@'%';"
```

### 测试连接
```bash
# 测试ecommerce用户连接
docker exec -it ecommerce-mariadb mysql -u ecommerce -ppassword -e "SHOW DATABASES;"
```

### 常见问题

1. **权限不足错误**
   - 确保使用root用户执行权限脚本
   - 检查用户是否存在：`SELECT User, Host FROM mysql.user WHERE User='ecommerce';`

2. **连接被拒绝**
   - 检查用户的Host设置
   - 确认密码正确
   - 验证网络连接

3. **数据库不存在**
   - 先执行完整的init-mariadb.sql脚本
   - 或手动创建数据库后再赋权

## 📊 数据库结构

### 微服务数据库分布
- **用户服务**: `ecommerce_user`
  - users, user_profiles, user_addresses
- **商品服务**: `ecommerce_product`  
  - categories, products, inventory_logs
- **订单服务**: `ecommerce_order`
  - shopping_carts, orders, order_items
- **通知服务**: `ecommerce_notification`
  - notification_templates, notifications
- **推荐服务**: `ecommerce_recommendation`
  - user_behaviors, recommendations

## 📝 维护说明

### 定期任务
1. 定期检查用户权限
2. 监控数据库连接
3. 备份权限配置
4. 审查访问日志

### 权限更新
当添加新的微服务数据库时，需要：
1. 更新init-mariadb.sql
2. 更新grant-permissions.sql  
3. 重新执行权限赋予脚本

---

**注意**: 本脚本适用于开发和测试环境。生产环境部署前请根据安全要求进行相应调整。
