# 数据库初始化脚本

## 📄 文件说明

- **`init-mariadb.sql`** - 完整的数据库初始化脚本，包含：
  - 创建所有微服务数据库
  - 创建数据表结构
  - 创建ecommerce用户并赋予权限
  - 插入初始数据

## 🚀 使用方法

### Docker环境（推荐）
```bash
# 执行初始化脚本
docker exec -i ecommerce-mariadb mysql -u root -prootpassword < init-mariadb.sql
```

### 直接连接数据库
```bash
# 连接到MariaDB并执行脚本
mysql -h localhost -u root -p < init-mariadb.sql
```

### 交互式执行
```bash
# 连接到数据库
docker exec -it ecommerce-mariadb mysql -u root -prootpassword

# 在MySQL命令行中执行
mysql> SOURCE /path/to/init-mariadb.sql;
```

## 🔐 权限配置

脚本会自动创建以下用户和权限：

- **用户名**: `ecommerce`
- **密码**: `password`
- **权限**: 对所有微服务数据库的完整访问权限

### 数据库列表
- `ecommerce` - 主数据库
- `ecommerce_user` - 用户服务数据库
- `ecommerce_product` - 商品服务数据库
- `ecommerce_order` - 订单服务数据库
- `ecommerce_notification` - 通知服务数据库
- `ecommerce_recommendation` - 推荐服务数据库

## 🛡️ 生产环境建议

在生产环境中，建议：

1. **修改默认密码**：
```sql
ALTER USER 'ecommerce'@'%' IDENTIFIED BY 'your_strong_password';
```

2. **限制访问来源**：
```sql
-- 替换 '%' 为具体IP或网段
CREATE USER 'ecommerce'@'10.0.0.%' IDENTIFIED BY 'password';
```

3. **使用最小权限原则**：
```sql
-- 根据需要授予具体权限而非ALL PRIVILEGES
GRANT SELECT, INSERT, UPDATE, DELETE ON database.* TO 'user'@'host';
```

---

**注意**: 本脚本适用于开发和测试环境。生产环境部署前请根据安全要求进行相应调整。
