package com.ecommerce.productservice.service.impl;

import com.ecommerce.productservice.dto.CategoryRequest;
import com.ecommerce.productservice.dto.CategoryResponse;
import com.ecommerce.productservice.dto.CategoryTreeResponse;
import com.ecommerce.productservice.entity.Category;
import com.ecommerce.productservice.exception.BadRequestException;
import com.ecommerce.productservice.exception.ResourceNotFoundException;
import com.ecommerce.productservice.repository.CategoryRepository;
import com.ecommerce.productservice.repository.ProductRepository;
import com.ecommerce.productservice.service.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品分类服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class CategoryServiceImpl implements CategoryService {

    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ProductRepository productRepository;

    @Override
    public CategoryResponse createCategory(CategoryRequest request) {
        logger.info("创建分类: {}", request.getName());

        // 验证分类名称是否已存在
        if (categoryRepository.existsByName(request.getName())) {
            throw new BadRequestException("分类名称已存在: " + request.getName());
        }

        // 验证父分类是否存在
        if (request.getParentId() != null) {
            Category parentCategory = categoryRepository.findById(request.getParentId())
                    .orElseThrow(() -> new ResourceNotFoundException("父分类不存在，ID: " + request.getParentId()));
            
            // 设置级别为父分类级别+1
            request.setLevel(parentCategory.getLevel() + 1);
        }

        // 设置排序值
        if (request.getSortOrder() == null || request.getSortOrder() == 0) {
            Integer maxSortOrder = request.getParentId() != null 
                ? categoryRepository.findMaxSortOrderByParentId(request.getParentId())
                : categoryRepository.findMaxSortOrderForRootCategories();
            request.setSortOrder(maxSortOrder + 1);
        }

        // 创建分类实体
        Category category = new Category();
        category.setName(request.getName());
        category.setDescription(request.getDescription());
        category.setParentId(request.getParentId());
        category.setLevel(request.getLevel());
        category.setSortOrder(request.getSortOrder());
        category.setIsActive(request.getIsActive());
        category.setImageUrl(request.getImageUrl());

        Category savedCategory = categoryRepository.save(category);
        logger.info("分类创建成功: {}", savedCategory.getName());

        return CategoryResponse.fromEntity(savedCategory);
    }

    @Override
    @CacheEvict(value = "categories", key = "#categoryId")
    public CategoryResponse updateCategory(Long categoryId, CategoryRequest request) {
        logger.info("更新分类: categoryId={}, name={}", categoryId, request.getName());

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        // 验证分类名称是否已存在（排除当前分类）
        if (categoryRepository.existsByNameAndIdNot(request.getName(), categoryId)) {
            throw new BadRequestException("分类名称已存在: " + request.getName());
        }

        // 验证父分类
        if (request.getParentId() != null) {
            if (request.getParentId().equals(categoryId)) {
                throw new BadRequestException("分类不能设置自己为父分类");
            }
            
            Category parentCategory = categoryRepository.findById(request.getParentId())
                    .orElseThrow(() -> new ResourceNotFoundException("父分类不存在，ID: " + request.getParentId()));
            
            // 检查是否会形成循环引用
            if (isCircularReference(categoryId, request.getParentId())) {
                throw new BadRequestException("不能设置子分类为父分类，会形成循环引用");
            }
            
            request.setLevel(parentCategory.getLevel() + 1);
        } else {
            request.setLevel(1);
        }

        // 更新分类信息
        category.setName(request.getName());
        category.setDescription(request.getDescription());
        category.setParentId(request.getParentId());
        category.setLevel(request.getLevel());
        category.setSortOrder(request.getSortOrder());
        category.setIsActive(request.getIsActive());
        category.setImageUrl(request.getImageUrl());

        Category updatedCategory = categoryRepository.save(category);
        logger.info("分类更新成功: {}", updatedCategory.getName());

        return CategoryResponse.fromEntity(updatedCategory);
    }

    @Override
    @CacheEvict(value = "categories", key = "#categoryId")
    public void deleteCategory(Long categoryId) {
        logger.info("删除分类: {}", categoryId);

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        // 检查是否有子分类
        if (categoryRepository.existsByParentId(categoryId)) {
            throw new BadRequestException("该分类下还有子分类，无法删除");
        }

        // 检查是否有商品
        if (productRepository.countByCategoryId(categoryId) > 0) {
            throw new BadRequestException("该分类下还有商品，无法删除");
        }

        categoryRepository.delete(category);
        logger.info("分类删除成功: {}", category.getName());
    }

    @Override
    @Cacheable(value = "categories", key = "#categoryId")
    public CategoryResponse getCategoryById(Long categoryId) {
        logger.debug("获取分类信息: {}", categoryId);

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        return CategoryResponse.fromEntity(category);
    }

    @Override
    public CategoryResponse getCategoryByName(String name) {
        logger.debug("根据名称获取分类: {}", name);

        Category category = categoryRepository.findByName(name)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，名称: " + name));

        return CategoryResponse.fromEntity(category);
    }

    @Override
    public Page<CategoryResponse> getAllCategories(Pageable pageable) {
        logger.debug("获取所有分类，分页参数: {}", pageable);

        Page<Category> categories = categoryRepository.findAll(pageable);
        return categories.map(CategoryResponse::fromEntity);
    }

    @Override
    public Page<CategoryResponse> getActiveCategories(Pageable pageable) {
        logger.debug("获取活跃分类，分页参数: {}", pageable);

        Page<Category> categories = categoryRepository.findByIsActiveTrue(pageable);
        return categories.map(CategoryResponse::fromEntity);
    }

    @Override
    public List<CategoryResponse> getRootCategories() {
        logger.debug("获取根分类列表");

        List<Category> categories = categoryRepository.findByParentIdIsNullOrderBySortOrderAsc();
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> getActiveRootCategories() {
        logger.debug("获取活跃根分类列表");

        List<Category> categories = categoryRepository.findByParentIdIsNullAndIsActiveTrueOrderBySortOrderAsc();
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> getSubCategories(Long parentId) {
        logger.debug("获取子分类列表: parentId={}", parentId);

        List<Category> categories = categoryRepository.findByParentIdOrderBySortOrderAsc(parentId);
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> getActiveSubCategories(Long parentId) {
        logger.debug("获取活跃子分类列表: parentId={}", parentId);

        List<Category> categories = categoryRepository.findByParentIdAndIsActiveTrueOrderBySortOrderAsc(parentId);
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryTreeResponse> getCategoryTree() {
        logger.debug("获取分类树结构");

        List<Category> allCategories = categoryRepository.findCategoryTree();
        return buildCategoryTree(allCategories, false);
    }

    @Override
    public List<CategoryTreeResponse> getActiveCategoryTree() {
        logger.debug("获取活跃分类树结构");

        List<Category> activeCategories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
        return buildCategoryTree(activeCategories, true);
    }

    @Override
    public List<CategoryResponse> getCategoriesByLevel(Integer level) {
        logger.debug("根据级别获取分类: level={}", level);

        List<Category> categories = categoryRepository.findByLevelOrderBySortOrderAsc(level);
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> searchCategories(String name) {
        logger.debug("搜索分类: name={}", name);

        List<Category> categories = categoryRepository.findByNameContaining(name);
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> searchActiveCategories(String name) {
        logger.debug("搜索活跃分类: name={}", name);

        List<Category> categories = categoryRepository.findByNameContainingAndIsActiveTrue(name);
        return categories.stream()
                .map(CategoryResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> getCategoriesWithProductCount() {
        logger.debug("获取分类及其商品数量");

        List<Object[]> results = categoryRepository.findCategoriesWithProductCount();
        return results.stream()
                .map(result -> {
                    Category category = (Category) result[0];
                    Long productCount = (Long) result[1];
                    return CategoryResponse.fromEntityWithProductCount(category, productCount);
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> getSubCategoriesWithProductCount(Long parentId) {
        logger.debug("获取子分类及其商品数量: parentId={}", parentId);

        List<Object[]> results = categoryRepository.findSubCategoriesWithProductCount(parentId);
        return results.stream()
                .map(result -> {
                    Category category = (Category) result[0];
                    Long productCount = (Long) result[1];
                    return CategoryResponse.fromEntityWithProductCount(category, productCount);
                })
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "categories", key = "#categoryId")
    public void enableCategory(Long categoryId) {
        logger.info("启用分类: {}", categoryId);

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        category.setIsActive(true);
        categoryRepository.save(category);
        logger.info("分类启用成功: {}", category.getName());
    }

    @Override
    @CacheEvict(value = "categories", key = "#categoryId")
    public void disableCategory(Long categoryId) {
        logger.info("禁用分类: {}", categoryId);

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        category.setIsActive(false);
        categoryRepository.save(category);
        logger.info("分类禁用成功: {}", category.getName());
    }

    @Override
    @CacheEvict(value = "categories", key = "#categoryId")
    public void updateCategorySortOrder(Long categoryId, Integer sortOrder) {
        logger.info("更新分类排序: categoryId={}, sortOrder={}", categoryId, sortOrder);

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        category.setSortOrder(sortOrder);
        categoryRepository.save(category);
        logger.info("分类排序更新成功: {}", category.getName());
    }

    @Override
    @CacheEvict(value = "categories", key = "#categoryId")
    public void moveCategoryToParent(Long categoryId, Long newParentId) {
        logger.info("移动分类: categoryId={}, newParentId={}", categoryId, newParentId);

        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("分类不存在，ID: " + categoryId));

        // 验证新父分类
        if (newParentId != null) {
            if (newParentId.equals(categoryId)) {
                throw new BadRequestException("分类不能设置自己为父分类");
            }

            Category newParent = categoryRepository.findById(newParentId)
                    .orElseThrow(() -> new ResourceNotFoundException("新父分类不存在，ID: " + newParentId));

            // 检查是否会形成循环引用
            if (isCircularReference(categoryId, newParentId)) {
                throw new BadRequestException("不能设置子分类为父分类，会形成循环引用");
            }

            category.setParentId(newParentId);
            category.setLevel(newParent.getLevel() + 1);
        } else {
            category.setParentId(null);
            category.setLevel(1);
        }

        categoryRepository.save(category);
        logger.info("分类移动成功: {}", category.getName());
    }

    @Override
    public boolean existsByName(String name) {
        return categoryRepository.existsByName(name);
    }

    @Override
    public boolean existsByNameAndIdNot(String name, Long id) {
        return categoryRepository.existsByNameAndIdNot(name, id);
    }

    @Override
    public boolean hasSubCategories(Long categoryId) {
        return categoryRepository.existsByParentId(categoryId);
    }

    @Override
    public boolean hasProducts(Long categoryId) {
        return productRepository.countByCategoryId(categoryId) > 0;
    }

    @Override
    public long countCategories() {
        return categoryRepository.count();
    }

    @Override
    public long countActiveCategories() {
        return categoryRepository.countByIsActiveTrue();
    }

    @Override
    public long countSubCategories(Long parentId) {
        return categoryRepository.countByParentId(parentId);
    }

    // 构建分类树的私有方法
    private List<CategoryTreeResponse> buildCategoryTree(List<Category> categories, boolean activeOnly) {
        Map<Long, CategoryTreeResponse> categoryMap = new HashMap<>();
        List<CategoryTreeResponse> rootCategories = new ArrayList<>();

        // 转换为CategoryTreeResponse并建立映射
        for (Category category : categories) {
            if (activeOnly && !category.isActive()) {
                continue;
            }
            CategoryTreeResponse treeNode = CategoryTreeResponse.fromEntity(category);
            categoryMap.put(category.getId(), treeNode);
        }

        // 构建树结构
        for (CategoryTreeResponse treeNode : categoryMap.values()) {
            if (treeNode.getParentId() == null) {
                rootCategories.add(treeNode);
            } else {
                CategoryTreeResponse parent = categoryMap.get(treeNode.getParentId());
                if (parent != null) {
                    parent.addChild(treeNode);
                }
            }
        }

        return rootCategories;
    }

    // 检查循环引用的私有方法
    private boolean isCircularReference(Long categoryId, Long parentId) {
        if (parentId == null) {
            return false;
        }

        Category parent = categoryRepository.findById(parentId).orElse(null);
        while (parent != null) {
            if (parent.getId().equals(categoryId)) {
                return true;
            }
            parent = parent.getParentId() != null
                ? categoryRepository.findById(parent.getParentId()).orElse(null)
                : null;
        }
        return false;
    }
}
