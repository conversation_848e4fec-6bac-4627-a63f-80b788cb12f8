package com.ecommerce.productservice.repository;

import com.ecommerce.productservice.entity.InventoryLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存变更日志数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface InventoryLogRepository extends JpaRepository<InventoryLog, Long> {

    /**
     * 根据商品ID查找库存日志
     */
    Page<InventoryLog> findByProductIdOrderByCreatedAtDesc(Long productId, Pageable pageable);

    /**
     * 根据商品ID和操作类型查找库存日志
     */
    Page<InventoryLog> findByProductIdAndOperationTypeOrderByCreatedAtDesc(
            Long productId, InventoryLog.OperationType operationType, Pageable pageable);

    /**
     * 根据操作类型查找库存日志
     */
    Page<InventoryLog> findByOperationTypeOrderByCreatedAtDesc(
            InventoryLog.OperationType operationType, Pageable pageable);

    /**
     * 根据操作人查找库存日志
     */
    Page<InventoryLog> findByOperatorIdOrderByCreatedAtDesc(Long operatorId, Pageable pageable);

    /**
     * 根据关联订单号查找库存日志
     */
    List<InventoryLog> findByRelatedOrderNoOrderByCreatedAtDesc(String relatedOrderNo);

    /**
     * 根据批次号查找库存日志
     */
    List<InventoryLog> findByBatchNoOrderByCreatedAtDesc(String batchNo);

    /**
     * 根据时间范围查找库存日志
     */
    @Query("SELECT il FROM InventoryLog il WHERE il.createdAt BETWEEN :startTime AND :endTime ORDER BY il.createdAt DESC")
    Page<InventoryLog> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             Pageable pageable);

    /**
     * 根据商品ID和时间范围查找库存日志
     */
    @Query("SELECT il FROM InventoryLog il WHERE il.product.id = :productId AND il.createdAt BETWEEN :startTime AND :endTime ORDER BY il.createdAt DESC")
    Page<InventoryLog> findByProductIdAndCreatedAtBetween(@Param("productId") Long productId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime,
                                                         Pageable pageable);

    /**
     * 统计商品的库存变更次数
     */
    long countByProductId(Long productId);

    /**
     * 统计商品指定操作类型的变更次数
     */
    long countByProductIdAndOperationType(Long productId, InventoryLog.OperationType operationType);

    /**
     * 统计时间范围内的库存变更次数
     */
    @Query("SELECT COUNT(il) FROM InventoryLog il WHERE il.createdAt BETWEEN :startTime AND :endTime")
    long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计商品在时间范围内的入库总量
     */
    @Query("SELECT COALESCE(SUM(il.quantityChange), 0) FROM InventoryLog il WHERE " +
           "il.product.id = :productId AND " +
           "il.operationType IN ('PURCHASE', 'RETURN', 'TRANSFER_IN', 'INITIAL') AND " +
           "il.createdAt BETWEEN :startTime AND :endTime")
    Integer sumInboundQuantityByProductIdAndDateRange(@Param("productId") Long productId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计商品在时间范围内的出库总量
     */
    @Query("SELECT COALESCE(SUM(ABS(il.quantityChange)), 0) FROM InventoryLog il WHERE " +
           "il.product.id = :productId AND " +
           "il.operationType IN ('SALE', 'REFUND', 'DAMAGE', 'TRANSFER_OUT') AND " +
           "il.createdAt BETWEEN :startTime AND :endTime")
    Integer sumOutboundQuantityByProductIdAndDateRange(@Param("productId") Long productId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 查找最近的库存日志
     */
    @Query("SELECT il FROM InventoryLog il WHERE il.product.id = :productId ORDER BY il.createdAt DESC")
    List<InventoryLog> findLatestByProductId(@Param("productId") Long productId, Pageable pageable);

    /**
     * 查找商品的最后一次库存记录
     */
    @Query("SELECT il FROM InventoryLog il WHERE il.product.id = :productId ORDER BY il.createdAt DESC LIMIT 1")
    InventoryLog findLatestByProductId(@Param("productId") Long productId);

    /**
     * 根据操作人和时间范围查找库存日志
     */
    @Query("SELECT il FROM InventoryLog il WHERE il.operatorId = :operatorId AND il.createdAt BETWEEN :startTime AND :endTime ORDER BY il.createdAt DESC")
    Page<InventoryLog> findByOperatorIdAndCreatedAtBetween(@Param("operatorId") Long operatorId,
                                                          @Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime,
                                                          Pageable pageable);

    /**
     * 查找异常库存变更（变更后库存为负数）
     */
    @Query("SELECT il FROM InventoryLog il WHERE il.quantityAfter < 0 ORDER BY il.createdAt DESC")
    List<InventoryLog> findAbnormalInventoryLogs();

    /**
     * 统计各操作类型的变更次数
     */
    @Query("SELECT il.operationType, COUNT(il) FROM InventoryLog il GROUP BY il.operationType")
    List<Object[]> countByOperationType();

    /**
     * 统计时间范围内各操作类型的变更次数
     */
    @Query("SELECT il.operationType, COUNT(il) FROM InventoryLog il WHERE il.createdAt BETWEEN :startTime AND :endTime GROUP BY il.operationType")
    List<Object[]> countByOperationTypeAndDateRange(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查找大量库存变更记录（变更数量超过阈值）
     */
    @Query("SELECT il FROM InventoryLog il WHERE ABS(il.quantityChange) >= :threshold ORDER BY il.createdAt DESC")
    List<InventoryLog> findLargeQuantityChanges(@Param("threshold") Integer threshold);
}
