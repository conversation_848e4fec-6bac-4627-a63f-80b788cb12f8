package com.ecommerce.productservice.dto;

import com.ecommerce.productservice.entity.Category;

import java.util.ArrayList;
import java.util.List;

/**
 * 分类树响应DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class CategoryTreeResponse {

    private Long id;
    private String name;
    private String description;
    private Long parentId;
    private Integer level;
    private Integer sortOrder;
    private Boolean isActive;
    private String imageUrl;
    private Long productCount;
    private List<CategoryTreeResponse> children = new ArrayList<>();

    // 构造函数
    public CategoryTreeResponse() {}

    // 静态工厂方法
    public static CategoryTreeResponse fromEntity(Category category) {
        CategoryTreeResponse response = new CategoryTreeResponse();
        response.setId(category.getId());
        response.setName(category.getName());
        response.setDescription(category.getDescription());
        response.setParentId(category.getParentId());
        response.setLevel(category.getLevel());
        response.setSortOrder(category.getSortOrder());
        response.setIsActive(category.getIsActive());
        response.setImageUrl(category.getImageUrl());
        
        // 设置商品数量
        if (category.getProducts() != null) {
            response.setProductCount((long) category.getProducts().size());
        }
        
        return response;
    }

    public static CategoryTreeResponse fromEntityWithProductCount(Category category, Long productCount) {
        CategoryTreeResponse response = fromEntity(category);
        response.setProductCount(productCount);
        return response;
    }

    // 添加子分类
    public void addChild(CategoryTreeResponse child) {
        this.children.add(child);
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Long getProductCount() {
        return productCount;
    }

    public void setProductCount(Long productCount) {
        this.productCount = productCount;
    }

    public List<CategoryTreeResponse> getChildren() {
        return children;
    }

    public void setChildren(List<CategoryTreeResponse> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return "CategoryTreeResponse{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", level=" + level +
                ", isActive=" + isActive +
                ", childrenCount=" + (children != null ? children.size() : 0) +
                '}';
    }
}
