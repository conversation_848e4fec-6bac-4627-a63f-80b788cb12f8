package com.ecommerce.productservice.dto;

import com.ecommerce.productservice.entity.Product;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 商品请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ProductRequest {

    @NotBlank(message = "商品名称不能为空")
    @Size(max = 200, message = "商品名称长度不能超过200个字符")
    private String name;

    @NotBlank(message = "商品SKU不能为空")
    @Size(max = 50, message = "SKU长度不能超过50个字符")
    private String sku;

    @Size(max = 1000, message = "商品描述长度不能超过1000个字符")
    private String description;

    private String detailedDescription;

    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "商品价格必须大于0")
    @Digits(integer = 10, fraction = 2, message = "价格格式不正确")
    private BigDecimal price;

    @DecimalMin(value = "0.0", message = "原价不能小于0")
    @Digits(integer = 10, fraction = 2, message = "原价格式不正确")
    private BigDecimal originalPrice;

    @Min(value = 0, message = "库存数量不能小于0")
    private Integer stockQuantity = 0;

    @Min(value = 0, message = "预警库存不能小于0")
    private Integer lowStockThreshold = 10;

    @Size(max = 50, message = "品牌名称长度不能超过50个字符")
    private String brand;

    @Size(max = 20, message = "重量单位长度不能超过20个字符")
    private String weightUnit = "kg";

    @DecimalMin(value = "0.0", message = "重量不能小于0")
    private BigDecimal weight;

    @Size(max = 20, message = "尺寸单位长度不能超过20个字符")
    private String dimensionUnit = "cm";

    @DecimalMin(value = "0.0", message = "长度不能小于0")
    private BigDecimal length;

    @DecimalMin(value = "0.0", message = "宽度不能小于0")
    private BigDecimal width;

    @DecimalMin(value = "0.0", message = "高度不能小于0")
    private BigDecimal height;

    private Product.ProductStatus status = Product.ProductStatus.ACTIVE;

    private Boolean isFeatured = false;

    @Size(max = 500, message = "主图URL长度不能超过500个字符")
    private String mainImageUrl;

    private String imageUrls; // JSON格式存储多个图片URL

    private String attributes; // JSON格式存储商品属性

    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    // 构造函数
    public ProductRequest() {}

    public ProductRequest(String name, String sku, BigDecimal price, Long categoryId) {
        this.name = name;
        this.sku = sku;
        this.price = price;
        this.categoryId = categoryId;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetailedDescription() {
        return detailedDescription;
    }

    public void setDetailedDescription(String detailedDescription) {
        this.detailedDescription = detailedDescription;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(Integer stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public Integer getLowStockThreshold() {
        return lowStockThreshold;
    }

    public void setLowStockThreshold(Integer lowStockThreshold) {
        this.lowStockThreshold = lowStockThreshold;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getDimensionUnit() {
        return dimensionUnit;
    }

    public void setDimensionUnit(String dimensionUnit) {
        this.dimensionUnit = dimensionUnit;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public Product.ProductStatus getStatus() {
        return status;
    }

    public void setStatus(Product.ProductStatus status) {
        this.status = status;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public String getMainImageUrl() {
        return mainImageUrl;
    }

    public void setMainImageUrl(String mainImageUrl) {
        this.mainImageUrl = mainImageUrl;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public String toString() {
        return "ProductRequest{" +
                "name='" + name + '\'' +
                ", sku='" + sku + '\'' +
                ", price=" + price +
                ", categoryId=" + categoryId +
                ", status=" + status +
                '}';
    }
}
