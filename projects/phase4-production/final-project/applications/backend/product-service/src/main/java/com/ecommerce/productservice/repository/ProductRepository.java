package com.ecommerce.productservice.repository;

import com.ecommerce.productservice.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 商品数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    /**
     * 根据SKU查找商品
     */
    Optional<Product> findBySku(String sku);

    /**
     * 检查SKU是否存在
     */
    boolean existsBySku(String sku);

    /**
     * 检查SKU是否存在（排除指定ID）
     */
    boolean existsBySkuAndIdNot(String sku, Long id);

    /**
     * 根据分类ID查找商品
     */
    Page<Product> findByCategoryId(Long categoryId, Pageable pageable);

    /**
     * 根据分类ID查找活跃商品
     */
    Page<Product> findByCategoryIdAndStatus(Long categoryId, Product.ProductStatus status, Pageable pageable);

    /**
     * 查找活跃商品
     */
    Page<Product> findByStatus(Product.ProductStatus status, Pageable pageable);

    /**
     * 根据名称模糊查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:name% ORDER BY p.createdAt DESC")
    Page<Product> findByNameContaining(@Param("name") String name, Pageable pageable);

    /**
     * 根据名称模糊查询活跃商品
     */
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:name% AND p.status = :status ORDER BY p.createdAt DESC")
    Page<Product> findByNameContainingAndStatus(@Param("name") String name, 
                                               @Param("status") Product.ProductStatus status, 
                                               Pageable pageable);

    /**
     * 根据品牌查找商品
     */
    Page<Product> findByBrand(String brand, Pageable pageable);

    /**
     * 根据品牌查找活跃商品
     */
    Page<Product> findByBrandAndStatus(String brand, Product.ProductStatus status, Pageable pageable);

    /**
     * 根据价格范围查找商品
     */
    @Query("SELECT p FROM Product p WHERE p.price BETWEEN :minPrice AND :maxPrice AND p.status = :status ORDER BY p.price ASC")
    Page<Product> findByPriceRange(@Param("minPrice") BigDecimal minPrice, 
                                  @Param("maxPrice") BigDecimal maxPrice,
                                  @Param("status") Product.ProductStatus status,
                                  Pageable pageable);

    /**
     * 查找特色商品
     */
    Page<Product> findByIsFeaturedTrueAndStatus(Product.ProductStatus status, Pageable pageable);

    /**
     * 查找库存不足的商品
     */
    @Query("SELECT p FROM Product p WHERE p.stockQuantity <= p.lowStockThreshold AND p.status = :status")
    List<Product> findLowStockProducts(@Param("status") Product.ProductStatus status);

    /**
     * 查找缺货商品
     */
    @Query("SELECT p FROM Product p WHERE p.stockQuantity = 0 AND p.status = :status")
    List<Product> findOutOfStockProducts(@Param("status") Product.ProductStatus status);

    /**
     * 根据销量排序查找商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status ORDER BY p.salesCount DESC")
    Page<Product> findByStatusOrderBySalesCountDesc(@Param("status") Product.ProductStatus status, Pageable pageable);

    /**
     * 根据评分排序查找商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status ORDER BY p.ratingAverage DESC, p.ratingCount DESC")
    Page<Product> findByStatusOrderByRatingDesc(@Param("status") Product.ProductStatus status, Pageable pageable);

    /**
     * 根据浏览量排序查找商品
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status ORDER BY p.viewCount DESC")
    Page<Product> findByStatusOrderByViewCountDesc(@Param("status") Product.ProductStatus status, Pageable pageable);

    /**
     * 综合搜索商品
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(p.name LIKE %:keyword% OR p.description LIKE %:keyword% OR p.brand LIKE %:keyword%) " +
           "AND p.status = :status " +
           "ORDER BY p.salesCount DESC, p.ratingAverage DESC")
    Page<Product> searchProducts(@Param("keyword") String keyword, 
                                @Param("status") Product.ProductStatus status,
                                Pageable pageable);

    /**
     * 根据分类和关键词搜索商品
     */
    @Query("SELECT p FROM Product p WHERE " +
           "p.category.id = :categoryId " +
           "AND (p.name LIKE %:keyword% OR p.description LIKE %:keyword% OR p.brand LIKE %:keyword%) " +
           "AND p.status = :status " +
           "ORDER BY p.salesCount DESC, p.ratingAverage DESC")
    Page<Product> searchProductsByCategory(@Param("categoryId") Long categoryId,
                                          @Param("keyword") String keyword,
                                          @Param("status") Product.ProductStatus status,
                                          Pageable pageable);

    /**
     * 更新商品浏览量
     */
    @Modifying
    @Query("UPDATE Product p SET p.viewCount = p.viewCount + 1 WHERE p.id = :productId")
    void incrementViewCount(@Param("productId") Long productId);

    /**
     * 更新商品销量
     */
    @Modifying
    @Query("UPDATE Product p SET p.salesCount = p.salesCount + :quantity WHERE p.id = :productId")
    void incrementSalesCount(@Param("productId") Long productId, @Param("quantity") int quantity);

    /**
     * 更新商品库存
     */
    @Modifying
    @Query("UPDATE Product p SET p.stockQuantity = p.stockQuantity - :quantity WHERE p.id = :productId AND p.stockQuantity >= :quantity")
    int decreaseStock(@Param("productId") Long productId, @Param("quantity") int quantity);

    /**
     * 增加商品库存
     */
    @Modifying
    @Query("UPDATE Product p SET p.stockQuantity = p.stockQuantity + :quantity WHERE p.id = :productId")
    void increaseStock(@Param("productId") Long productId, @Param("quantity") int quantity);

    /**
     * 更新商品评分
     */
    @Modifying
    @Query("UPDATE Product p SET p.ratingAverage = :ratingAverage, p.ratingCount = :ratingCount WHERE p.id = :productId")
    void updateRating(@Param("productId") Long productId, 
                     @Param("ratingAverage") BigDecimal ratingAverage, 
                     @Param("ratingCount") Integer ratingCount);

    /**
     * 统计分类下的商品数量
     */
    long countByCategoryId(Long categoryId);

    /**
     * 统计分类下的活跃商品数量
     */
    long countByCategoryIdAndStatus(Long categoryId, Product.ProductStatus status);

    /**
     * 统计品牌下的商品数量
     */
    long countByBrand(String brand);

    /**
     * 查找所有品牌
     */
    @Query("SELECT DISTINCT p.brand FROM Product p WHERE p.brand IS NOT NULL AND p.status = :status ORDER BY p.brand")
    List<String> findDistinctBrands(@Param("status") Product.ProductStatus status);

    /**
     * 查找分类下的所有品牌
     */
    @Query("SELECT DISTINCT p.brand FROM Product p WHERE p.category.id = :categoryId AND p.brand IS NOT NULL AND p.status = :status ORDER BY p.brand")
    List<String> findDistinctBrandsByCategory(@Param("categoryId") Long categoryId, @Param("status") Product.ProductStatus status);

    /**
     * 查找商品价格范围
     */
    @Query("SELECT MIN(p.price), MAX(p.price) FROM Product p WHERE p.status = :status")
    Object[] findPriceRange(@Param("status") Product.ProductStatus status);

    /**
     * 查找分类下商品价格范围
     */
    @Query("SELECT MIN(p.price), MAX(p.price) FROM Product p WHERE p.category.id = :categoryId AND p.status = :status")
    Object[] findPriceRangeByCategory(@Param("categoryId") Long categoryId, @Param("status") Product.ProductStatus status);
}
