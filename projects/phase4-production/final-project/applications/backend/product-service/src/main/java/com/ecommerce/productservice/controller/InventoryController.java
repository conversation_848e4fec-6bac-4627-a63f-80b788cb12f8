package com.ecommerce.productservice.controller;

import com.ecommerce.productservice.dto.ApiResponse;
import com.ecommerce.productservice.dto.InventoryLogResponse;
import com.ecommerce.productservice.entity.InventoryLog;
import com.ecommerce.productservice.security.UserPrincipal;
import com.ecommerce.productservice.service.InventoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 库存管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@PreAuthorize("hasRole('ADMIN')")
public class InventoryController {

    private static final Logger logger = LoggerFactory.getLogger(InventoryController.class);

    @Autowired
    private InventoryService inventoryService;

    /**
     * 采购入库
     */
    @PostMapping("/{productId}/inventory/purchase")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> purchaseInbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("采购入库: productId={}, quantity={}, operator={}", 
                   productId, quantity, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.purchaseInbound(
            productId, quantity, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("采购入库成功", response));
    }

    /**
     * 销售出库
     */
    @PostMapping("/{productId}/inventory/sale")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> saleOutbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam String orderNo,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("销售出库: productId={}, quantity={}, orderNo={}, operator={}", 
                   productId, quantity, orderNo, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.saleOutbound(
            productId, quantity, orderNo, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("销售出库成功", response));
    }

    /**
     * 退货入库
     */
    @PostMapping("/{productId}/inventory/return")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> returnInbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam String orderNo,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("退货入库: productId={}, quantity={}, orderNo={}, operator={}", 
                   productId, quantity, orderNo, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.returnInbound(
            productId, quantity, orderNo, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("退货入库成功", response));
    }

    /**
     * 退款出库
     */
    @PostMapping("/{productId}/inventory/refund")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> refundOutbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam String orderNo,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("退款出库: productId={}, quantity={}, orderNo={}, operator={}", 
                   productId, quantity, orderNo, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.refundOutbound(
            productId, quantity, orderNo, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("退款出库成功", response));
    }

    /**
     * 库存调整
     */
    @PostMapping("/{productId}/inventory/adjust")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> adjustInventory(
            @PathVariable Long productId,
            @RequestParam Integer quantityChange,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("库存调整: productId={}, quantityChange={}, operator={}", 
                   productId, quantityChange, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.adjustInventory(
            productId, quantityChange, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("库存调整成功", response));
    }

    /**
     * 损坏出库
     */
    @PostMapping("/{productId}/inventory/damage")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> damageOutbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("损坏出库: productId={}, quantity={}, operator={}", 
                   productId, quantity, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.damageOutbound(
            productId, quantity, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("损坏出库成功", response));
    }

    /**
     * 调拨入库
     */
    @PostMapping("/{productId}/inventory/transfer-in")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> transferInbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("调拨入库: productId={}, quantity={}, operator={}", 
                   productId, quantity, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.transferInbound(
            productId, quantity, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("调拨入库成功", response));
    }

    /**
     * 调拨出库
     */
    @PostMapping("/{productId}/inventory/transfer-out")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> transferOutbound(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("调拨出库: productId={}, quantity={}, operator={}", 
                   productId, quantity, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.transferOutbound(
            productId, quantity, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("调拨出库成功", response));
    }

    /**
     * 初始化库存
     */
    @PostMapping("/{productId}/inventory/initialize")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> initializeInventory(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("初始化库存: productId={}, quantity={}, operator={}", 
                   productId, quantity, currentUser.getUsername());
        
        InventoryLogResponse response = inventoryService.initializeInventory(
            productId, quantity, remarks, currentUser.getUsername(), currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("库存初始化成功", response));
    }

    /**
     * 获取商品库存日志
     */
    @GetMapping("/{productId}/inventory/logs")
    public ResponseEntity<ApiResponse<Page<InventoryLogResponse>>> getProductInventoryLogs(
            @PathVariable Long productId,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("获取商品库存日志: productId={}", productId);
        
        Page<InventoryLogResponse> response = inventoryService.getProductInventoryLogs(productId, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据操作类型获取商品库存日志
     */
    @GetMapping("/{productId}/inventory/logs/type/{operationType}")
    public ResponseEntity<ApiResponse<Page<InventoryLogResponse>>> getProductInventoryLogsByType(
            @PathVariable Long productId,
            @PathVariable InventoryLog.OperationType operationType,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据操作类型获取商品库存日志: productId={}, operationType={}", productId, operationType);
        
        Page<InventoryLogResponse> response = inventoryService.getProductInventoryLogsByType(
            productId, operationType, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据操作类型获取库存日志
     */
    @GetMapping("/inventory/logs/type/{operationType}")
    public ResponseEntity<ApiResponse<Page<InventoryLogResponse>>> getInventoryLogsByType(
            @PathVariable InventoryLog.OperationType operationType,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据操作类型获取库存日志: operationType={}", operationType);
        
        Page<InventoryLogResponse> response = inventoryService.getInventoryLogsByType(operationType, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据操作人获取库存日志
     */
    @GetMapping("/inventory/logs/operator/{operatorId}")
    public ResponseEntity<ApiResponse<Page<InventoryLogResponse>>> getInventoryLogsByOperator(
            @PathVariable Long operatorId,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据操作人获取库存日志: operatorId={}", operatorId);
        
        Page<InventoryLogResponse> response = inventoryService.getInventoryLogsByOperator(operatorId, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据订单号获取库存日志
     */
    @GetMapping("/inventory/logs/order/{orderNo}")
    public ResponseEntity<ApiResponse<List<InventoryLogResponse>>> getInventoryLogsByOrderNo(
            @PathVariable String orderNo) {
        logger.debug("根据订单号获取库存日志: orderNo={}", orderNo);
        
        List<InventoryLogResponse> response = inventoryService.getInventoryLogsByOrderNo(orderNo);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据批次号获取库存日志
     */
    @GetMapping("/inventory/logs/batch/{batchNo}")
    public ResponseEntity<ApiResponse<List<InventoryLogResponse>>> getInventoryLogsByBatchNo(
            @PathVariable String batchNo) {
        logger.debug("根据批次号获取库存日志: batchNo={}", batchNo);
        
        List<InventoryLogResponse> response = inventoryService.getInventoryLogsByBatchNo(batchNo);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据时间范围获取库存日志
     */
    @GetMapping("/inventory/logs/date-range")
    public ResponseEntity<ApiResponse<Page<InventoryLogResponse>>> getInventoryLogsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据时间范围获取库存日志: startTime={}, endTime={}", startTime, endTime);

        Page<InventoryLogResponse> response = inventoryService.getInventoryLogsByDateRange(
            startTime, endTime, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据商品和时间范围获取库存日志
     */
    @GetMapping("/{productId}/inventory/logs/date-range")
    public ResponseEntity<ApiResponse<Page<InventoryLogResponse>>> getProductInventoryLogsByDateRange(
            @PathVariable Long productId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据商品和时间范围获取库存日志: productId={}, startTime={}, endTime={}",
                    productId, startTime, endTime);

        Page<InventoryLogResponse> response = inventoryService.getProductInventoryLogsByDateRange(
            productId, startTime, endTime, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取商品最近的库存日志
     */
    @GetMapping("/{productId}/inventory/logs/latest")
    public ResponseEntity<ApiResponse<List<InventoryLogResponse>>> getLatestInventoryLogs(
            @PathVariable Long productId,
            @RequestParam(defaultValue = "10") int limit) {
        logger.debug("获取商品最近的库存日志: productId={}, limit={}", productId, limit);

        List<InventoryLogResponse> response = inventoryService.getLatestInventoryLogs(productId, limit);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取商品最后一次库存记录
     */
    @GetMapping("/{productId}/inventory/logs/last")
    public ResponseEntity<ApiResponse<InventoryLogResponse>> getLatestInventoryLog(
            @PathVariable Long productId) {
        logger.debug("获取商品最后一次库存记录: productId={}", productId);

        InventoryLogResponse response = inventoryService.getLatestInventoryLog(productId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取异常库存变更记录
     */
    @GetMapping("/inventory/logs/abnormal")
    public ResponseEntity<ApiResponse<List<InventoryLogResponse>>> getAbnormalInventoryLogs() {
        logger.debug("获取异常库存变更记录");

        List<InventoryLogResponse> response = inventoryService.getAbnormalInventoryLogs();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取大量库存变更记录
     */
    @GetMapping("/inventory/logs/large-changes")
    public ResponseEntity<ApiResponse<List<InventoryLogResponse>>> getLargeQuantityChanges(
            @RequestParam(defaultValue = "100") Integer threshold) {
        logger.debug("获取大量库存变更记录: threshold={}", threshold);

        List<InventoryLogResponse> response = inventoryService.getLargeQuantityChanges(threshold);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 检查库存是否充足
     */
    @GetMapping("/{productId}/inventory/check")
    public ResponseEntity<ApiResponse<Boolean>> checkStockSufficiency(
            @PathVariable Long productId,
            @RequestParam Integer requiredQuantity) {
        logger.debug("检查库存是否充足: productId={}, requiredQuantity={}", productId, requiredQuantity);

        boolean sufficient = inventoryService.isStockSufficient(productId, requiredQuantity);
        return ResponseEntity.ok(ApiResponse.success(sufficient));
    }

    /**
     * 批量检查库存是否充足
     */
    @PostMapping("/inventory/check-batch")
    public ResponseEntity<ApiResponse<Map<Long, Boolean>>> checkStockSufficiencyBatch(
            @RequestBody Map<Long, Integer> productQuantities) {
        logger.debug("批量检查库存是否充足: {}", productQuantities);

        Map<Long, Boolean> response = inventoryService.checkStockSufficiency(productQuantities);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取当前库存量
     */
    @GetMapping("/{productId}/inventory/current")
    public ResponseEntity<ApiResponse<Integer>> getCurrentStock(@PathVariable Long productId) {
        logger.debug("获取当前库存量: productId={}", productId);

        Integer currentStock = inventoryService.getCurrentStock(productId);
        return ResponseEntity.ok(ApiResponse.success(currentStock));
    }

    /**
     * 批量获取当前库存量
     */
    @PostMapping("/inventory/current-batch")
    public ResponseEntity<ApiResponse<Map<Long, Integer>>> getCurrentStocksBatch(
            @RequestBody List<Long> productIds) {
        logger.debug("批量获取当前库存量: {}", productIds);

        Map<Long, Integer> response = inventoryService.getCurrentStocks(productIds);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 统计商品库存变更次数
     */
    @GetMapping("/{productId}/inventory/stats/changes")
    public ResponseEntity<ApiResponse<Long>> countProductInventoryChanges(@PathVariable Long productId) {
        logger.debug("统计商品库存变更次数: productId={}", productId);

        long count = inventoryService.countProductInventoryChanges(productId);
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    /**
     * 统计商品在时间范围内的入库总量
     */
    @GetMapping("/{productId}/inventory/stats/inbound")
    public ResponseEntity<ApiResponse<Integer>> sumInboundQuantity(
            @PathVariable Long productId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("统计商品入库总量: productId={}, startTime={}, endTime={}",
                    productId, startTime, endTime);

        Integer quantity = inventoryService.sumInboundQuantity(productId, startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success(quantity));
    }

    /**
     * 统计商品在时间范围内的出库总量
     */
    @GetMapping("/{productId}/inventory/stats/outbound")
    public ResponseEntity<ApiResponse<Integer>> sumOutboundQuantity(
            @PathVariable Long productId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("统计商品出库总量: productId={}, startTime={}, endTime={}",
                    productId, startTime, endTime);

        Integer quantity = inventoryService.sumOutboundQuantity(productId, startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success(quantity));
    }

    /**
     * 统计各操作类型的变更次数
     */
    @GetMapping("/inventory/stats/operation-types")
    public ResponseEntity<ApiResponse<Map<InventoryLog.OperationType, Long>>> countByOperationType() {
        logger.debug("统计各操作类型的变更次数");

        Map<InventoryLog.OperationType, Long> response = inventoryService.countByOperationType();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 统计时间范围内各操作类型的变更次数
     */
    @GetMapping("/inventory/stats/operation-types/date-range")
    public ResponseEntity<ApiResponse<Map<InventoryLog.OperationType, Long>>> countByOperationTypeAndDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("统计时间范围内各操作类型的变更次数: startTime={}, endTime={}", startTime, endTime);

        Map<InventoryLog.OperationType, Long> response = inventoryService.countByOperationTypeAndDateRange(
            startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
}
