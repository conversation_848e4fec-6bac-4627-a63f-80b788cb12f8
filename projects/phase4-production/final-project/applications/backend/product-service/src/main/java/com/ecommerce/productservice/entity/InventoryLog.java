package com.ecommerce.productservice.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 库存变更日志实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Entity
@Table(name = "inventory_logs", indexes = {
    @Index(name = "idx_inventory_product", columnList = "product_id"),
    @Index(name = "idx_inventory_type", columnList = "operation_type"),
    @Index(name = "idx_inventory_created", columnList = "created_at")
})
@EntityListeners(AuditingEntityListener.class)
public class InventoryLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "操作类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false, length = 20)
    private OperationType operationType;

    @NotNull(message = "变更数量不能为空")
    @Column(name = "quantity_change", nullable = false)
    private Integer quantityChange;

    @NotNull(message = "变更前库存不能为空")
    @Column(name = "quantity_before", nullable = false)
    private Integer quantityBefore;

    @NotNull(message = "变更后库存不能为空")
    @Column(name = "quantity_after", nullable = false)
    private Integer quantityAfter;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remarks", length = 500)
    private String remarks;

    @Size(max = 100, message = "操作人长度不能超过100个字符")
    @Column(name = "operator", length = 100)
    private String operator;

    @Column(name = "operator_id")
    private Long operatorId;

    @Size(max = 100, message = "关联订单号长度不能超过100个字符")
    @Column(name = "related_order_no", length = 100)
    private String relatedOrderNo;

    @Size(max = 100, message = "关联批次号长度不能超过100个字符")
    @Column(name = "batch_no", length = 100)
    private String batchNo;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;

    // 库存操作类型枚举
    public enum OperationType {
        PURCHASE("采购入库"),
        SALE("销售出库"),
        RETURN("退货入库"),
        REFUND("退款出库"),
        ADJUSTMENT("库存调整"),
        DAMAGE("损坏出库"),
        TRANSFER_IN("调拨入库"),
        TRANSFER_OUT("调拨出库"),
        INITIAL("初始库存");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public InventoryLog() {}

    public InventoryLog(Product product, OperationType operationType, Integer quantityChange,
                       Integer quantityBefore, Integer quantityAfter) {
        this.product = product;
        this.operationType = operationType;
        this.quantityChange = quantityChange;
        this.quantityBefore = quantityBefore;
        this.quantityAfter = quantityAfter;
    }

    public InventoryLog(Product product, OperationType operationType, Integer quantityChange,
                       Integer quantityBefore, Integer quantityAfter, String remarks, String operator) {
        this(product, operationType, quantityChange, quantityBefore, quantityAfter);
        this.remarks = remarks;
        this.operator = operator;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public OperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(OperationType operationType) {
        this.operationType = operationType;
    }

    public Integer getQuantityChange() {
        return quantityChange;
    }

    public void setQuantityChange(Integer quantityChange) {
        this.quantityChange = quantityChange;
    }

    public Integer getQuantityBefore() {
        return quantityBefore;
    }

    public void setQuantityBefore(Integer quantityBefore) {
        this.quantityBefore = quantityBefore;
    }

    public Integer getQuantityAfter() {
        return quantityAfter;
    }

    public void setQuantityAfter(Integer quantityAfter) {
        this.quantityAfter = quantityAfter;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getRelatedOrderNo() {
        return relatedOrderNo;
    }

    public void setRelatedOrderNo(String relatedOrderNo) {
        this.relatedOrderNo = relatedOrderNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    // 业务方法
    public boolean isInbound() {
        return operationType == OperationType.PURCHASE || 
               operationType == OperationType.RETURN || 
               operationType == OperationType.TRANSFER_IN ||
               operationType == OperationType.INITIAL ||
               (operationType == OperationType.ADJUSTMENT && quantityChange > 0);
    }

    public boolean isOutbound() {
        return operationType == OperationType.SALE || 
               operationType == OperationType.REFUND || 
               operationType == OperationType.DAMAGE ||
               operationType == OperationType.TRANSFER_OUT ||
               (operationType == OperationType.ADJUSTMENT && quantityChange < 0);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        InventoryLog that = (InventoryLog) o;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "InventoryLog{" +
                "id=" + id +
                ", operationType=" + operationType +
                ", quantityChange=" + quantityChange +
                ", quantityBefore=" + quantityBefore +
                ", quantityAfter=" + quantityAfter +
                ", createdAt=" + createdAt +
                '}';
    }
}
