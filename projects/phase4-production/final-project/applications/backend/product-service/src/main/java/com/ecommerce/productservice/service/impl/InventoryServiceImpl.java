package com.ecommerce.productservice.service.impl;

import com.ecommerce.productservice.dto.InventoryLogResponse;
import com.ecommerce.productservice.entity.InventoryLog;
import com.ecommerce.productservice.entity.Product;
import com.ecommerce.productservice.exception.ResourceNotFoundException;
import com.ecommerce.productservice.repository.InventoryLogRepository;
import com.ecommerce.productservice.repository.ProductRepository;
import com.ecommerce.productservice.service.InventoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class InventoryServiceImpl implements InventoryService {

    private static final Logger logger = LoggerFactory.getLogger(InventoryServiceImpl.class);

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private InventoryLogRepository inventoryLogRepository;

    @Override
    public void purchaseStock(Long productId, Integer quantity, String remarks, String operator) {
        logger.info("采购入库: productId={}, quantity={}, operator={}", productId, quantity, operator);

        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("商品不存在: " + productId));

        // 更新库存
        product.setStockQuantity(product.getStockQuantity() + quantity);
        productRepository.save(product);

        logger.info("采购入库完成: productId={}, newStock={}", productId, product.getStockQuantity());
    }

    @Override
    public void saleStock(Long productId, Integer quantity, String orderNo, String operator) {
        logger.info("销售出库: productId={}, quantity={}, orderNo={}", productId, quantity, orderNo);

        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("商品不存在: " + productId));

        if (product.getStockQuantity() < quantity) {
            throw new IllegalArgumentException("库存不足，当前库存: " + product.getStockQuantity());
        }

        // 更新库存
        product.setStockQuantity(product.getStockQuantity() - quantity);
        productRepository.save(product);

        logger.info("销售出库完成: productId={}, newStock={}", productId, product.getStockQuantity());
    }

    @Override
    public void adjustStock(Long productId, Integer quantity, String remarks, String operator) {
        logger.info("库存调整: productId={}, quantity={}, operator={}", productId, quantity, operator);

        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("商品不存在: " + productId));

        Integer oldStock = product.getStockQuantity();
        product.setStockQuantity(quantity);
        productRepository.save(product);

        logger.info("库存调整完成: productId={}, oldStock={}, newStock={}", productId, oldStock, quantity);
    }

    @Override
    public List<InventoryLogResponse> getInventoryLogs(Long productId) {
        logger.debug("获取商品库存日志: productId={}", productId);
        // 简化实现，返回空列表
        return List.of();
    }

    @Override
    public List<InventoryLogResponse> getAllInventoryLogs() {
        logger.debug("获取所有库存日志");
        // 简化实现，返回空列表
        return List.of();
    }

    @Override
    public Integer getCurrentStock(Long productId) {
        logger.debug("获取当前库存量: productId={}", productId);

        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("商品不存在: " + productId));

        return product.getStockQuantity();
    }

    @Override
    public Map<Long, Integer> getCurrentStocks(List<Long> productIds) {
        logger.debug("批量获取当前库存量: productIds={}", productIds);

        Map<Long, Integer> stocks = new HashMap<>();
        for (Long productId : productIds) {
            try {
                Integer stock = getCurrentStock(productId);
                stocks.put(productId, stock);
            } catch (ResourceNotFoundException e) {
                stocks.put(productId, 0);
            }
        }
        return stocks;
    }

    @Override
    public boolean isStockSufficient(Long productId, Integer requiredQuantity) {
        logger.debug("检查库存是否充足: productId={}, requiredQuantity={}", productId, requiredQuantity);

        Integer currentStock = getCurrentStock(productId);
        return currentStock >= requiredQuantity;
    }
}
