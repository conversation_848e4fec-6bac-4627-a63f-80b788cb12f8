package com.ecommerce.productservice.controller;

import com.ecommerce.productservice.dto.ApiResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
public class HealthController {

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${info.app.version}")
    private String version;

    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("service", applicationName);
        healthInfo.put("version", version);
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(ApiResponse.success("服务运行正常", healthInfo));
    }
}
