package com.ecommerce.productservice.repository;

import com.ecommerce.productservice.entity.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 商品分类数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    /**
     * 根据名称查找分类
     */
    Optional<Category> findByName(String name);

    /**
     * 检查分类名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查分类名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(String name, Long id);

    /**
     * 根据父分类ID查找子分类
     */
    List<Category> findByParentIdOrderBySortOrderAsc(Long parentId);

    /**
     * 查找根分类（父分类ID为空）
     */
    List<Category> findByParentIdIsNullOrderBySortOrderAsc();

    /**
     * 根据级别查找分类
     */
    List<Category> findByLevelOrderBySortOrderAsc(Integer level);

    /**
     * 查找活跃的分类
     */
    List<Category> findByIsActiveTrueOrderBySortOrderAsc();

    /**
     * 根据父分类ID查找活跃的子分类
     */
    List<Category> findByParentIdAndIsActiveTrueOrderBySortOrderAsc(Long parentId);

    /**
     * 查找活跃的根分类
     */
    List<Category> findByParentIdIsNullAndIsActiveTrueOrderBySortOrderAsc();

    /**
     * 分页查询分类
     */
    Page<Category> findByIsActiveTrue(Pageable pageable);

    /**
     * 根据名称模糊查询分类
     */
    @Query("SELECT c FROM Category c WHERE c.name LIKE %:name% ORDER BY c.sortOrder ASC")
    List<Category> findByNameContaining(@Param("name") String name);

    /**
     * 根据名称模糊查询活跃分类
     */
    @Query("SELECT c FROM Category c WHERE c.name LIKE %:name% AND c.isActive = true ORDER BY c.sortOrder ASC")
    List<Category> findByNameContainingAndIsActiveTrue(@Param("name") String name);

    /**
     * 查询分类及其商品数量
     */
    @Query("SELECT c, COUNT(p) FROM Category c LEFT JOIN c.products p WHERE c.isActive = true GROUP BY c ORDER BY c.sortOrder ASC")
    List<Object[]> findCategoriesWithProductCount();

    /**
     * 查询指定父分类下的子分类及其商品数量
     */
    @Query("SELECT c, COUNT(p) FROM Category c LEFT JOIN c.products p WHERE c.parentId = :parentId AND c.isActive = true GROUP BY c ORDER BY c.sortOrder ASC")
    List<Object[]> findSubCategoriesWithProductCount(@Param("parentId") Long parentId);

    /**
     * 查询分类树结构
     */
    @Query("SELECT c FROM Category c WHERE c.isActive = true ORDER BY c.level ASC, c.sortOrder ASC")
    List<Category> findCategoryTree();

    /**
     * 查询最大排序值
     */
    @Query("SELECT COALESCE(MAX(c.sortOrder), 0) FROM Category c WHERE c.parentId = :parentId")
    Integer findMaxSortOrderByParentId(@Param("parentId") Long parentId);

    /**
     * 查询最大排序值（根分类）
     */
    @Query("SELECT COALESCE(MAX(c.sortOrder), 0) FROM Category c WHERE c.parentId IS NULL")
    Integer findMaxSortOrderForRootCategories();

    /**
     * 统计子分类数量
     */
    long countByParentId(Long parentId);

    /**
     * 统计活跃分类数量
     */
    long countByIsActiveTrue();

    /**
     * 统计活跃子分类数量
     */
    long countByParentIdAndIsActiveTrue(Long parentId);

    /**
     * 检查是否有子分类
     */
    boolean existsByParentId(Long parentId);

    /**
     * 检查是否有活跃的子分类
     */
    boolean existsByParentIdAndIsActiveTrue(Long parentId);
}
