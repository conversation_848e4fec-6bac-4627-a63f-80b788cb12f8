package com.ecommerce.productservice.service;

import com.ecommerce.productservice.dto.InventoryLogResponse;

import java.util.List;
import java.util.Map;

/**
 * 库存服务接口（简化版）
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InventoryService {

    /**
     * 采购入库
     */
    void purchaseStock(Long productId, Integer quantity, String remarks, String operator);

    /**
     * 销售出库
     */
    void saleStock(Long productId, Integer quantity, String orderNo, String operator);

    /**
     * 库存调整
     */
    void adjustStock(Long productId, Integer quantity, String remarks, String operator);

    /**
     * 获取库存日志
     */
    List<InventoryLogResponse> getInventoryLogs(Long productId);

    /**
     * 获取所有库存日志
     */
    List<InventoryLogResponse> getAllInventoryLogs();

    /**
     * 获取当前库存量
     */
    Integer getCurrentStock(Long productId);

    /**
     * 批量获取当前库存量
     */
    Map<Long, Integer> getCurrentStocks(List<Long> productIds);

    /**
     * 检查库存是否充足
     */
    boolean isStockSufficient(Long productId, Integer requiredQuantity);
}
