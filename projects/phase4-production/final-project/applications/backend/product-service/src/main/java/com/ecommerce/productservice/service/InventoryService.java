package com.ecommerce.productservice.service;

import com.ecommerce.productservice.dto.InventoryLogResponse;
import com.ecommerce.productservice.entity.InventoryLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 库存管理服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InventoryService {

    /**
     * 记录库存变更
     */
    InventoryLogResponse recordInventoryChange(Long productId, 
                                             InventoryLog.OperationType operationType,
                                             Integer quantityChange, 
                                             String remarks,
                                             String operator,
                                             Long operatorId);

    /**
     * 记录库存变更（带订单号）
     */
    InventoryLogResponse recordInventoryChange(Long productId, 
                                             InventoryLog.OperationType operationType,
                                             Integer quantityChange, 
                                             String remarks,
                                             String operator,
                                             Long operatorId,
                                             String relatedOrderNo);

    /**
     * 记录库存变更（带批次号）
     */
    InventoryLogResponse recordInventoryChangeWithBatch(Long productId, 
                                                       InventoryLog.OperationType operationType,
                                                       Integer quantityChange, 
                                                       String remarks,
                                                       String operator,
                                                       Long operatorId,
                                                       String batchNo);

    /**
     * 采购入库
     */
    InventoryLogResponse purchaseInbound(Long productId, Integer quantity, String remarks, String operator, Long operatorId);

    /**
     * 销售出库
     */
    InventoryLogResponse saleOutbound(Long productId, Integer quantity, String orderNo, String operator, Long operatorId);

    /**
     * 退货入库
     */
    InventoryLogResponse returnInbound(Long productId, Integer quantity, String orderNo, String remarks, String operator, Long operatorId);

    /**
     * 退款出库
     */
    InventoryLogResponse refundOutbound(Long productId, Integer quantity, String orderNo, String remarks, String operator, Long operatorId);

    /**
     * 库存调整
     */
    InventoryLogResponse adjustInventory(Long productId, Integer quantityChange, String remarks, String operator, Long operatorId);

    /**
     * 损坏出库
     */
    InventoryLogResponse damageOutbound(Long productId, Integer quantity, String remarks, String operator, Long operatorId);

    /**
     * 调拨入库
     */
    InventoryLogResponse transferInbound(Long productId, Integer quantity, String remarks, String operator, Long operatorId);

    /**
     * 调拨出库
     */
    InventoryLogResponse transferOutbound(Long productId, Integer quantity, String remarks, String operator, Long operatorId);

    /**
     * 初始化库存
     */
    InventoryLogResponse initializeInventory(Long productId, Integer quantity, String remarks, String operator, Long operatorId);

    /**
     * 获取商品库存日志
     */
    Page<InventoryLogResponse> getProductInventoryLogs(Long productId, Pageable pageable);

    /**
     * 根据操作类型获取商品库存日志
     */
    Page<InventoryLogResponse> getProductInventoryLogsByType(Long productId, 
                                                           InventoryLog.OperationType operationType, 
                                                           Pageable pageable);

    /**
     * 根据操作类型获取库存日志
     */
    Page<InventoryLogResponse> getInventoryLogsByType(InventoryLog.OperationType operationType, Pageable pageable);

    /**
     * 根据操作人获取库存日志
     */
    Page<InventoryLogResponse> getInventoryLogsByOperator(Long operatorId, Pageable pageable);

    /**
     * 根据订单号获取库存日志
     */
    List<InventoryLogResponse> getInventoryLogsByOrderNo(String orderNo);

    /**
     * 根据批次号获取库存日志
     */
    List<InventoryLogResponse> getInventoryLogsByBatchNo(String batchNo);

    /**
     * 根据时间范围获取库存日志
     */
    Page<InventoryLogResponse> getInventoryLogsByDateRange(LocalDateTime startTime, 
                                                         LocalDateTime endTime, 
                                                         Pageable pageable);

    /**
     * 根据商品和时间范围获取库存日志
     */
    Page<InventoryLogResponse> getProductInventoryLogsByDateRange(Long productId,
                                                                LocalDateTime startTime, 
                                                                LocalDateTime endTime, 
                                                                Pageable pageable);

    /**
     * 根据操作人和时间范围获取库存日志
     */
    Page<InventoryLogResponse> getInventoryLogsByOperatorAndDateRange(Long operatorId,
                                                                    LocalDateTime startTime, 
                                                                    LocalDateTime endTime, 
                                                                    Pageable pageable);

    /**
     * 获取商品最近的库存日志
     */
    List<InventoryLogResponse> getLatestInventoryLogs(Long productId, int limit);

    /**
     * 获取商品最后一次库存记录
     */
    InventoryLogResponse getLatestInventoryLog(Long productId);

    /**
     * 获取异常库存变更记录
     */
    List<InventoryLogResponse> getAbnormalInventoryLogs();

    /**
     * 获取大量库存变更记录
     */
    List<InventoryLogResponse> getLargeQuantityChanges(Integer threshold);

    /**
     * 统计商品库存变更次数
     */
    long countProductInventoryChanges(Long productId);

    /**
     * 统计商品指定操作类型的变更次数
     */
    long countProductInventoryChangesByType(Long productId, InventoryLog.OperationType operationType);

    /**
     * 统计时间范围内的库存变更次数
     */
    long countInventoryChangesByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计商品在时间范围内的入库总量
     */
    Integer sumInboundQuantity(Long productId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计商品在时间范围内的出库总量
     */
    Integer sumOutboundQuantity(Long productId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计各操作类型的变更次数
     */
    Map<InventoryLog.OperationType, Long> countByOperationType();

    /**
     * 统计时间范围内各操作类型的变更次数
     */
    Map<InventoryLog.OperationType, Long> countByOperationTypeAndDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 检查库存是否充足
     */
    boolean isStockSufficient(Long productId, Integer requiredQuantity);

    /**
     * 批量检查库存是否充足
     */
    Map<Long, Boolean> checkStockSufficiency(Map<Long, Integer> productQuantities);

    /**
     * 获取当前库存量
     */
    Integer getCurrentStock(Long productId);

    /**
     * 批量获取当前库存量
     */
    Map<Long, Integer> getCurrentStocks(List<Long> productIds);
}
