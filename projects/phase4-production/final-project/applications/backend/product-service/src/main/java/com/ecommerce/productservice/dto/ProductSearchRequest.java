package com.ecommerce.productservice.dto;

import com.ecommerce.productservice.entity.Product;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品搜索请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ProductSearchRequest {

    private String keyword;
    private Long categoryId;
    private List<Long> categoryIds;
    private String brand;
    private List<String> brands;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private Product.ProductStatus status;
    private Boolean isFeatured;
    private Boolean inStock;
    private String sortBy; // price, sales, rating, viewCount, createdAt
    private String sortDirection; // asc, desc

    // 构造函数
    public ProductSearchRequest() {}

    public ProductSearchRequest(String keyword) {
        this.keyword = keyword;
    }

    // Getters and Setters
    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public List<Long> getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(List<Long> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public List<String> getBrands() {
        return brands;
    }

    public void setBrands(List<String> brands) {
        this.brands = brands;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Product.ProductStatus getStatus() {
        return status;
    }

    public void setStatus(Product.ProductStatus status) {
        this.status = status;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public Boolean getInStock() {
        return inStock;
    }

    public void setInStock(Boolean inStock) {
        this.inStock = inStock;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    @Override
    public String toString() {
        return "ProductSearchRequest{" +
                "keyword='" + keyword + '\'' +
                ", categoryId=" + categoryId +
                ", brand='" + brand + '\'' +
                ", minPrice=" + minPrice +
                ", maxPrice=" + maxPrice +
                ", status=" + status +
                ", sortBy='" + sortBy + '\'' +
                ", sortDirection='" + sortDirection + '\'' +
                '}';
    }
}
