package com.ecommerce.productservice.controller;

import com.ecommerce.productservice.dto.ApiResponse;
import com.ecommerce.productservice.dto.CategoryRequest;
import com.ecommerce.productservice.dto.CategoryResponse;
import com.ecommerce.productservice.dto.CategoryTreeResponse;
import com.ecommerce.productservice.service.CategoryService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品分类控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/products/categories")
public class CategoryController {

    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);

    @Autowired
    private CategoryService categoryService;

    /**
     * 创建分类
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<CategoryResponse>> createCategory(@Valid @RequestBody CategoryRequest request) {
        logger.info("创建分类请求: {}", request.getName());
        
        CategoryResponse response = categoryService.createCategory(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("分类创建成功", response));
    }

    /**
     * 更新分类
     */
    @PutMapping("/{categoryId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<CategoryResponse>> updateCategory(
            @PathVariable Long categoryId,
            @Valid @RequestBody CategoryRequest request) {
        logger.info("更新分类请求: categoryId={}, name={}", categoryId, request.getName());
        
        CategoryResponse response = categoryService.updateCategory(categoryId, request);
        return ResponseEntity.ok(ApiResponse.success("分类更新成功", response));
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{categoryId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable Long categoryId) {
        logger.info("删除分类请求: categoryId={}", categoryId);
        
        categoryService.deleteCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success("分类删除成功"));
    }

    /**
     * 根据ID获取分类
     */
    @GetMapping("/{categoryId}")
    public ResponseEntity<ApiResponse<CategoryResponse>> getCategoryById(@PathVariable Long categoryId) {
        logger.debug("获取分类详情: categoryId={}", categoryId);
        
        CategoryResponse response = categoryService.getCategoryById(categoryId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取所有分类（分页）
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<CategoryResponse>>> getAllCategories(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(defaultValue = "false") boolean activeOnly) {
        logger.debug("获取分类列表: pageable={}, activeOnly={}", pageable, activeOnly);
        
        Page<CategoryResponse> response = activeOnly 
            ? categoryService.getActiveCategories(pageable)
            : categoryService.getAllCategories(pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取根分类列表
     */
    @GetMapping("/root")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getRootCategories(
            @RequestParam(defaultValue = "false") boolean activeOnly) {
        logger.debug("获取根分类列表: activeOnly={}", activeOnly);
        
        List<CategoryResponse> response = activeOnly 
            ? categoryService.getActiveRootCategories()
            : categoryService.getRootCategories();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取子分类列表
     */
    @GetMapping("/{parentId}/children")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getSubCategories(
            @PathVariable Long parentId,
            @RequestParam(defaultValue = "false") boolean activeOnly) {
        logger.debug("获取子分类列表: parentId={}, activeOnly={}", parentId, activeOnly);
        
        List<CategoryResponse> response = activeOnly 
            ? categoryService.getActiveSubCategories(parentId)
            : categoryService.getSubCategories(parentId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取分类树结构
     */
    @GetMapping("/tree")
    public ResponseEntity<ApiResponse<List<CategoryTreeResponse>>> getCategoryTree(
            @RequestParam(defaultValue = "false") boolean activeOnly) {
        logger.debug("获取分类树结构: activeOnly={}", activeOnly);
        
        List<CategoryTreeResponse> response = activeOnly 
            ? categoryService.getActiveCategoryTree()
            : categoryService.getCategoryTree();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 搜索分类
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> searchCategories(
            @RequestParam String name,
            @RequestParam(defaultValue = "false") boolean activeOnly) {
        logger.debug("搜索分类: name={}, activeOnly={}", name, activeOnly);
        
        List<CategoryResponse> response = activeOnly 
            ? categoryService.searchActiveCategories(name)
            : categoryService.searchCategories(name);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取分类及其商品数量
     */
    @GetMapping("/with-product-count")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getCategoriesWithProductCount() {
        logger.debug("获取分类及其商品数量");
        
        List<CategoryResponse> response = categoryService.getCategoriesWithProductCount();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取子分类及其商品数量
     */
    @GetMapping("/{parentId}/children/with-product-count")
    public ResponseEntity<ApiResponse<List<CategoryResponse>>> getSubCategoriesWithProductCount(
            @PathVariable Long parentId) {
        logger.debug("获取子分类及其商品数量: parentId={}", parentId);
        
        List<CategoryResponse> response = categoryService.getSubCategoriesWithProductCount(parentId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 启用分类
     */
    @PutMapping("/{categoryId}/enable")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> enableCategory(@PathVariable Long categoryId) {
        logger.info("启用分类: categoryId={}", categoryId);
        
        categoryService.enableCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success("分类启用成功"));
    }

    /**
     * 禁用分类
     */
    @PutMapping("/{categoryId}/disable")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> disableCategory(@PathVariable Long categoryId) {
        logger.info("禁用分类: categoryId={}", categoryId);
        
        categoryService.disableCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success("分类禁用成功"));
    }

    /**
     * 更新分类排序
     */
    @PutMapping("/{categoryId}/sort-order")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateCategorySortOrder(
            @PathVariable Long categoryId,
            @RequestParam Integer sortOrder) {
        logger.info("更新分类排序: categoryId={}, sortOrder={}", categoryId, sortOrder);
        
        categoryService.updateCategorySortOrder(categoryId, sortOrder);
        return ResponseEntity.ok(ApiResponse.success("分类排序更新成功"));
    }

    /**
     * 移动分类到新的父分类下
     */
    @PutMapping("/{categoryId}/move")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> moveCategoryToParent(
            @PathVariable Long categoryId,
            @RequestParam(required = false) Long newParentId) {
        logger.info("移动分类: categoryId={}, newParentId={}", categoryId, newParentId);
        
        categoryService.moveCategoryToParent(categoryId, newParentId);
        return ResponseEntity.ok(ApiResponse.success("分类移动成功"));
    }

    /**
     * 检查分类名称是否存在
     */
    @GetMapping("/check-name")
    public ResponseEntity<ApiResponse<Boolean>> checkCategoryName(
            @RequestParam String name,
            @RequestParam(required = false) Long excludeId) {
        logger.debug("检查分类名称: name={}, excludeId={}", name, excludeId);
        
        boolean exists = excludeId != null 
            ? categoryService.existsByNameAndIdNot(name, excludeId)
            : categoryService.existsByName(name);
        return ResponseEntity.ok(ApiResponse.success(!exists));
    }

    /**
     * 获取分类统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Object>> getCategoryStats() {
        logger.debug("获取分类统计信息");
        
        long totalCategories = categoryService.countCategories();
        long activeCategories = categoryService.countActiveCategories();
        
        return ResponseEntity.ok(ApiResponse.success(new Object() {
            public final long total = totalCategories;
            public final long active = activeCategories;
            public final long inactive = totalCategories - activeCategories;
        }));
    }
}
