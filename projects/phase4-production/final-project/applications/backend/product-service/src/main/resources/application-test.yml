# 测试环境配置
server:
  port: 8082
  servlet:
    context-path: /api/products

spring:
  application:
    name: product-service
  profiles:
    active: test

  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  h2:
    console:
      enabled: true
      path: /h2-console

  jpa:
    hibernate:
      ddl-auto: create-drop
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # Redis配置（可选，测试时可以禁用）
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    property-naming-strategy: LOWER_CAMEL_CASE
    default-property-inclusion: NON_NULL

# JWT配置
jwt:
  secret: ecommerce-product-service-test-secret-key
  expiration: 86400000

# 日志配置
logging:
  level:
    com.ecommerce.productservice: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
