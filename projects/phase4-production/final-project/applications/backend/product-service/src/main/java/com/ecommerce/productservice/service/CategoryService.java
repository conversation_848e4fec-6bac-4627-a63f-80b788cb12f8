package com.ecommerce.productservice.service;

import com.ecommerce.productservice.dto.CategoryRequest;
import com.ecommerce.productservice.dto.CategoryResponse;
import com.ecommerce.productservice.dto.CategoryTreeResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 商品分类服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface CategoryService {

    /**
     * 创建分类
     */
    CategoryResponse createCategory(CategoryRequest request);

    /**
     * 更新分类
     */
    CategoryResponse updateCategory(Long categoryId, CategoryRequest request);

    /**
     * 删除分类
     */
    void deleteCategory(Long categoryId);

    /**
     * 根据ID获取分类
     */
    CategoryResponse getCategoryById(Long categoryId);

    /**
     * 根据名称获取分类
     */
    CategoryResponse getCategoryByName(String name);

    /**
     * 获取所有分类（分页）
     */
    Page<CategoryResponse> getAllCategories(Pageable pageable);

    /**
     * 获取活跃分类（分页）
     */
    Page<CategoryResponse> getActiveCategories(Pageable pageable);

    /**
     * 获取根分类列表
     */
    List<CategoryResponse> getRootCategories();

    /**
     * 获取活跃根分类列表
     */
    List<CategoryResponse> getActiveRootCategories();

    /**
     * 获取子分类列表
     */
    List<CategoryResponse> getSubCategories(Long parentId);

    /**
     * 获取活跃子分类列表
     */
    List<CategoryResponse> getActiveSubCategories(Long parentId);

    /**
     * 获取分类树结构
     */
    List<CategoryTreeResponse> getCategoryTree();

    /**
     * 获取活跃分类树结构
     */
    List<CategoryTreeResponse> getActiveCategoryTree();

    /**
     * 根据级别获取分类
     */
    List<CategoryResponse> getCategoriesByLevel(Integer level);

    /**
     * 根据名称搜索分类
     */
    List<CategoryResponse> searchCategories(String name);

    /**
     * 根据名称搜索活跃分类
     */
    List<CategoryResponse> searchActiveCategories(String name);

    /**
     * 获取分类及其商品数量
     */
    List<CategoryResponse> getCategoriesWithProductCount();

    /**
     * 获取子分类及其商品数量
     */
    List<CategoryResponse> getSubCategoriesWithProductCount(Long parentId);

    /**
     * 启用分类
     */
    void enableCategory(Long categoryId);

    /**
     * 禁用分类
     */
    void disableCategory(Long categoryId);

    /**
     * 更新分类排序
     */
    void updateCategorySortOrder(Long categoryId, Integer sortOrder);

    /**
     * 移动分类到新的父分类下
     */
    void moveCategoryToParent(Long categoryId, Long newParentId);

    /**
     * 检查分类名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查分类名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(String name, Long id);

    /**
     * 检查分类是否有子分类
     */
    boolean hasSubCategories(Long categoryId);

    /**
     * 检查分类是否有商品
     */
    boolean hasProducts(Long categoryId);

    /**
     * 统计分类数量
     */
    long countCategories();

    /**
     * 统计活跃分类数量
     */
    long countActiveCategories();

    /**
     * 统计子分类数量
     */
    long countSubCategories(Long parentId);
}
