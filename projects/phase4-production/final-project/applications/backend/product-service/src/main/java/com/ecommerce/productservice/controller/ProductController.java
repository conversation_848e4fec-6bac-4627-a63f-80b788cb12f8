package com.ecommerce.productservice.controller;

import com.ecommerce.productservice.dto.ApiResponse;
import com.ecommerce.productservice.dto.ProductRequest;
import com.ecommerce.productservice.dto.ProductResponse;
import com.ecommerce.productservice.dto.ProductSearchRequest;
import com.ecommerce.productservice.entity.Product;
import com.ecommerce.productservice.service.ProductService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/products")
public class ProductController {

    private static final Logger logger = LoggerFactory.getLogger(ProductController.class);

    @Autowired
    private ProductService productService;

    /**
     * 创建商品
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<ProductResponse>> createProduct(@Valid @RequestBody ProductRequest request) {
        logger.info("创建商品请求: {}", request.getName());
        
        ProductResponse response = productService.createProduct(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("商品创建成功", response));
    }

    /**
     * 更新商品
     */
    @PutMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<ProductResponse>> updateProduct(
            @PathVariable Long productId,
            @Valid @RequestBody ProductRequest request) {
        logger.info("更新商品请求: productId={}, name={}", productId, request.getName());
        
        ProductResponse response = productService.updateProduct(productId, request);
        return ResponseEntity.ok(ApiResponse.success("商品更新成功", response));
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteProduct(@PathVariable Long productId) {
        logger.info("删除商品请求: productId={}", productId);
        
        productService.deleteProduct(productId);
        return ResponseEntity.ok(ApiResponse.success("商品删除成功"));
    }

    /**
     * 根据ID获取商品
     */
    @GetMapping("/{productId}")
    public ResponseEntity<ApiResponse<ProductResponse>> getProductById(@PathVariable Long productId) {
        logger.debug("获取商品详情: productId={}", productId);
        
        ProductResponse response = productService.getProductById(productId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取商品详情（包含浏览量统计）
     */
    @GetMapping("/{productId}/details")
    public ResponseEntity<ApiResponse<ProductResponse>> getProductDetails(@PathVariable Long productId) {
        logger.debug("获取商品详情并统计浏览量: productId={}", productId);
        
        ProductResponse response = productService.getProductDetails(productId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据SKU获取商品
     */
    @GetMapping("/sku/{sku}")
    public ResponseEntity<ApiResponse<ProductResponse>> getProductBySku(@PathVariable String sku) {
        logger.debug("根据SKU获取商品: sku={}", sku);
        
        ProductResponse response = productService.getProductBySku(sku);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取商品列表（分页）
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getProducts(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(defaultValue = "true") boolean activeOnly) {
        logger.debug("获取商品列表: pageable={}, activeOnly={}", pageable, activeOnly);
        
        Page<ProductResponse> response = activeOnly 
            ? productService.getActiveProducts(pageable)
            : productService.getAllProducts(pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据分类获取商品
     */
    @GetMapping("/category/{categoryId}")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getProductsByCategory(
            @PathVariable Long categoryId,
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(defaultValue = "true") boolean activeOnly) {
        logger.debug("根据分类获取商品: categoryId={}, activeOnly={}", categoryId, activeOnly);
        
        Page<ProductResponse> response = activeOnly 
            ? productService.getActiveProductsByCategory(categoryId, pageable)
            : productService.getProductsByCategory(categoryId, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据品牌获取商品
     */
    @GetMapping("/brand/{brand}")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getProductsByBrand(
            @PathVariable String brand,
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(defaultValue = "true") boolean activeOnly) {
        logger.debug("根据品牌获取商品: brand={}, activeOnly={}", brand, activeOnly);
        
        Page<ProductResponse> response = activeOnly 
            ? productService.getActiveProductsByBrand(brand, pageable)
            : productService.getProductsByBrand(brand, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据价格范围获取商品
     */
    @GetMapping("/price-range")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getProductsByPriceRange(
            @RequestParam BigDecimal minPrice,
            @RequestParam BigDecimal maxPrice,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据价格范围获取商品: minPrice={}, maxPrice={}", minPrice, maxPrice);
        
        Page<ProductResponse> response = productService.getProductsByPriceRange(minPrice, maxPrice, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取特色商品
     */
    @GetMapping("/featured")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getFeaturedProducts(
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("获取特色商品");
        
        Page<ProductResponse> response = productService.getFeaturedProducts(pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据销量排序获取商品
     */
    @GetMapping("/best-sellers")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getBestSellers(
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("获取热销商品");
        
        Page<ProductResponse> response = productService.getProductsBySales(pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据评分排序获取商品
     */
    @GetMapping("/top-rated")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getTopRatedProducts(
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("获取高评分商品");
        
        Page<ProductResponse> response = productService.getProductsByRating(pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据浏览量排序获取商品
     */
    @GetMapping("/most-viewed")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> getMostViewedProducts(
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("获取热门浏览商品");
        
        Page<ProductResponse> response = productService.getProductsByViewCount(pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 搜索商品
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> searchProducts(
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("搜索商品: keyword={}", keyword);
        
        Page<ProductResponse> response = productService.searchProducts(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 高级搜索商品
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> advancedSearchProducts(
            @RequestBody ProductSearchRequest request,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("高级搜索商品: {}", request);
        
        Page<ProductResponse> response = productService.searchProducts(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据分类和关键词搜索商品
     */
    @GetMapping("/search/category/{categoryId}")
    public ResponseEntity<ApiResponse<Page<ProductResponse>>> searchProductsByCategory(
            @PathVariable Long categoryId,
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("根据分类搜索商品: categoryId={}, keyword={}", categoryId, keyword);
        
        Page<ProductResponse> response = productService.searchProductsByCategory(categoryId, keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取库存不足的商品
     */
    @GetMapping("/low-stock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<ProductResponse>>> getLowStockProducts() {
        logger.debug("获取库存不足的商品");
        
        List<ProductResponse> response = productService.getLowStockProducts();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取缺货商品
     */
    @GetMapping("/out-of-stock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<ProductResponse>>> getOutOfStockProducts() {
        logger.debug("获取缺货商品");
        
        List<ProductResponse> response = productService.getOutOfStockProducts();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 更新商品状态
     */
    @PutMapping("/{productId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateProductStatus(
            @PathVariable Long productId,
            @RequestParam Product.ProductStatus status) {
        logger.info("更新商品状态: productId={}, status={}", productId, status);
        
        productService.updateProductStatus(productId, status);
        return ResponseEntity.ok(ApiResponse.success("商品状态更新成功"));
    }

    /**
     * 上架商品
     */
    @PutMapping("/{productId}/activate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> activateProduct(@PathVariable Long productId) {
        logger.info("上架商品: productId={}", productId);
        
        productService.activateProduct(productId);
        return ResponseEntity.ok(ApiResponse.success("商品上架成功"));
    }

    /**
     * 下架商品
     */
    @PutMapping("/{productId}/deactivate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deactivateProduct(@PathVariable Long productId) {
        logger.info("下架商品: productId={}", productId);
        
        productService.deactivateProduct(productId);
        return ResponseEntity.ok(ApiResponse.success("商品下架成功"));
    }

    /**
     * 设置特色商品
     */
    @PutMapping("/{productId}/featured")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> setFeaturedProduct(
            @PathVariable Long productId,
            @RequestParam boolean featured) {
        logger.info("设置特色商品: productId={}, featured={}", productId, featured);

        productService.setFeaturedProduct(productId, featured);
        return ResponseEntity.ok(ApiResponse.success(featured ? "设置为特色商品成功" : "取消特色商品成功"));
    }

    /**
     * 更新商品库存
     */
    @PutMapping("/{productId}/stock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateStock(
            @PathVariable Long productId,
            @RequestParam Integer quantity) {
        logger.info("更新商品库存: productId={}, quantity={}", productId, quantity);

        productService.updateStock(productId, quantity);
        return ResponseEntity.ok(ApiResponse.success("库存更新成功"));
    }

    /**
     * 增加库存
     */
    @PutMapping("/{productId}/stock/increase")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> increaseStock(
            @PathVariable Long productId,
            @RequestParam Integer quantity) {
        logger.info("增加商品库存: productId={}, quantity={}", productId, quantity);

        productService.increaseStock(productId, quantity);
        return ResponseEntity.ok(ApiResponse.success("库存增加成功"));
    }

    /**
     * 减少库存
     */
    @PutMapping("/{productId}/stock/decrease")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> decreaseStock(
            @PathVariable Long productId,
            @RequestParam Integer quantity) {
        logger.info("减少商品库存: productId={}, quantity={}", productId, quantity);

        boolean success = productService.decreaseStock(productId, quantity);
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("库存减少成功"));
        } else {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "库存不足"));
        }
    }

    /**
     * 更新商品评分
     */
    @PutMapping("/{productId}/rating")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateRating(
            @PathVariable Long productId,
            @RequestParam BigDecimal ratingAverage,
            @RequestParam Integer ratingCount) {
        logger.info("更新商品评分: productId={}, ratingAverage={}, ratingCount={}",
                   productId, ratingAverage, ratingCount);

        productService.updateRating(productId, ratingAverage, ratingCount);
        return ResponseEntity.ok(ApiResponse.success("评分更新成功"));
    }

    /**
     * 检查SKU是否存在
     */
    @GetMapping("/check-sku")
    public ResponseEntity<ApiResponse<Boolean>> checkSku(
            @RequestParam String sku,
            @RequestParam(required = false) Long excludeId) {
        logger.debug("检查SKU: sku={}, excludeId={}", sku, excludeId);

        boolean exists = excludeId != null
            ? productService.existsBySkuAndIdNot(sku, excludeId)
            : productService.existsBySku(sku);
        return ResponseEntity.ok(ApiResponse.success(!exists));
    }

    /**
     * 获取所有品牌
     */
    @GetMapping("/brands")
    public ResponseEntity<ApiResponse<List<String>>> getAllBrands() {
        logger.debug("获取所有品牌");

        List<String> response = productService.getAllBrands();
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取分类下的所有品牌
     */
    @GetMapping("/brands/category/{categoryId}")
    public ResponseEntity<ApiResponse<List<String>>> getBrandsByCategory(@PathVariable Long categoryId) {
        logger.debug("获取分类下的品牌: categoryId={}", categoryId);

        List<String> response = productService.getBrandsByCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取商品价格范围
     */
    @GetMapping("/price-range/info")
    public ResponseEntity<ApiResponse<Object>> getPriceRange() {
        logger.debug("获取商品价格范围");

        BigDecimal[] priceRange = productService.getPriceRange();
        return ResponseEntity.ok(ApiResponse.success(new Object() {
            public final BigDecimal minPrice = priceRange[0];
            public final BigDecimal maxPrice = priceRange[1];
        }));
    }

    /**
     * 获取分类下商品价格范围
     */
    @GetMapping("/price-range/category/{categoryId}")
    public ResponseEntity<ApiResponse<Object>> getPriceRangeByCategory(@PathVariable Long categoryId) {
        logger.debug("获取分类下商品价格范围: categoryId={}", categoryId);

        BigDecimal[] priceRange = productService.getPriceRangeByCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success(new Object() {
            public final BigDecimal minPrice = priceRange[0];
            public final BigDecimal maxPrice = priceRange[1];
        }));
    }

    /**
     * 获取商品统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Object>> getProductStats() {
        logger.debug("获取商品统计信息");

        long totalProducts = productService.countProducts();
        long activeProducts = productService.countActiveProducts();

        return ResponseEntity.ok(ApiResponse.success(new Object() {
            public final long total = totalProducts;
            public final long active = activeProducts;
            public final long inactive = totalProducts - activeProducts;
        }));
    }

    /**
     * 获取分类下的商品统计
     */
    @GetMapping("/stats/category/{categoryId}")
    public ResponseEntity<ApiResponse<Object>> getProductStatsByCategory(@PathVariable Long categoryId) {
        logger.debug("获取分类下的商品统计: categoryId={}", categoryId);

        long productCount = productService.countProductsByCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success(new Object() {
            public final long count = productCount;
        }));
    }

    /**
     * 获取品牌下的商品统计
     */
    @GetMapping("/stats/brand/{brand}")
    public ResponseEntity<ApiResponse<Object>> getProductStatsByBrand(@PathVariable String brand) {
        logger.debug("获取品牌下的商品统计: brand={}", brand);

        long productCount = productService.countProductsByBrand(brand);
        return ResponseEntity.ok(ApiResponse.success(new Object() {
            public final long count = productCount;
        }));
    }
}
