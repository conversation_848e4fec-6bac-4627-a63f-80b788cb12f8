package com.ecommerce.productservice.service;

import com.ecommerce.productservice.dto.ProductRequest;
import com.ecommerce.productservice.dto.ProductResponse;
import com.ecommerce.productservice.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品服务接口（简化版）
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ProductService {

    /**
     * 创建商品
     */
    ProductResponse createProduct(ProductRequest request);

    /**
     * 更新商品
     */
    ProductResponse updateProduct(Long productId, ProductRequest request);

    /**
     * 删除商品
     */
    void deleteProduct(Long productId);

    /**
     * 根据ID获取商品
     */
    ProductResponse getProductById(Long productId);

    /**
     * 根据SKU获取商品
     */
    ProductResponse getProductBySku(String sku);

    /**
     * 获取商品详情（包含浏览量统计）
     */
    ProductResponse getProductDetails(Long productId);

    /**
     * 获取商品列表
     */
    Page<ProductResponse> getProducts(Pageable pageable);

    /**
     * 获取所有商品（分页）
     */
    Page<ProductResponse> getAllProducts(Pageable pageable);

    /**
     * 获取活跃商品（分页）
     */
    Page<ProductResponse> getActiveProducts(Pageable pageable);

    /**
     * 根据分类获取商品
     */
    Page<ProductResponse> getProductsByCategory(Long categoryId, Pageable pageable);

    /**
     * 根据分类获取活跃商品
     */
    Page<ProductResponse> getActiveProductsByCategory(Long categoryId, Pageable pageable);

    /**
     * 根据品牌获取商品
     */
    Page<ProductResponse> getProductsByBrand(String brand, Pageable pageable);

    /**
     * 根据品牌获取活跃商品
     */
    Page<ProductResponse> getActiveProductsByBrand(String brand, Pageable pageable);

    /**
     * 根据ID列表获取商品
     */
    List<ProductResponse> getProductsByIds(List<Long> productIds);

    /**
     * 根据SKU列表获取商品
     */
    List<ProductResponse> getProductsBySkus(List<String> skus);

    /**
     * 根据价格范围获取商品
     */
    Page<ProductResponse> getProductsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);

    /**
     * 根据状态获取商品
     */
    Page<ProductResponse> getProductsByStatus(Product.ProductStatus status, Pageable pageable);

    /**
     * 获取特色商品
     */
    Page<ProductResponse> getFeaturedProducts(Pageable pageable);

    /**
     * 获取新品
     */
    Page<ProductResponse> getNewProducts(Pageable pageable);

    /**
     * 获取热销商品
     */
    Page<ProductResponse> getPopularProducts(Pageable pageable);

    /**
     * 获取高评分商品
     */
    Page<ProductResponse> getTopRatedProducts(Pageable pageable);

    /**
     * 获取促销商品
     */
    Page<ProductResponse> getDiscountedProducts(Pageable pageable);

    /**
     * 根据销量排序获取商品
     */
    Page<ProductResponse> getProductsBySales(Pageable pageable);

    /**
     * 根据评分排序获取商品
     */
    Page<ProductResponse> getProductsByRating(Pageable pageable);

    /**
     * 根据浏览量排序获取商品
     */
    Page<ProductResponse> getProductsByViewCount(Pageable pageable);

    /**
     * 搜索商品
     */
    Page<ProductResponse> searchProducts(String keyword, Pageable pageable);

    /**
     * 根据分类和关键词搜索商品
     */
    Page<ProductResponse> searchProductsByCategory(Long categoryId, String keyword, Pageable pageable);

    /**
     * 获取低库存商品
     */
    Page<ProductResponse> getLowStockProducts(Pageable pageable);

    /**
     * 获取缺货商品
     */
    Page<ProductResponse> getOutOfStockProducts(Pageable pageable);

    /**
     * 获取相关商品
     */
    List<ProductResponse> getRelatedProducts(Long productId, int limit);

    /**
     * 获取推荐商品
     */
    List<ProductResponse> getRecommendedProducts(Long userId, int limit);

    /**
     * 更新商品库存
     */
    void updateProductStock(Long productId, Integer stockQuantity);

    /**
     * 更新商品价格
     */
    void updateProductPrice(Long productId, BigDecimal price);

    /**
     * 更新商品状态
     */
    void updateProductStatus(Long productId, Product.ProductStatus status);

    /**
     * 上架商品
     */
    void activateProduct(Long productId);

    /**
     * 下架商品
     */
    void deactivateProduct(Long productId);

    /**
     * 设置特色商品
     */
    void setFeaturedProduct(Long productId, boolean featured);

    /**
     * 更新商品库存
     */
    void updateStock(Long productId, Integer quantity);

    /**
     * 增加库存
     */
    void increaseStock(Long productId, Integer quantity);

    /**
     * 减少库存
     */
    boolean decreaseStock(Long productId, Integer quantity);

    /**
     * 更新商品评分
     */
    void updateRating(Long productId, BigDecimal ratingAverage, Integer ratingCount);

    /**
     * 增加商品浏览量
     */
    void incrementViewCount(Long productId);

    /**
     * 增加商品销量
     */
    void incrementSalesCount(Long productId, Long quantity);

    /**
     * 更新商品评分
     */
    void updateProductRating(Long productId, BigDecimal rating, Integer ratingCount);

    /**
     * 检查商品是否可用
     */
    boolean isProductAvailable(Long productId);

    /**
     * 检查库存是否充足
     */
    boolean isStockSufficient(Long productId, Integer requiredQuantity);

    /**
     * 获取商品库存
     */
    Integer getProductStock(Long productId);

    /**
     * 获取商品价格
     */
    BigDecimal getProductPrice(Long productId);

    /**
     * 统计商品总数
     */
    long countProducts();

    /**
     * 统计分类下的商品数量
     */
    long countProductsByCategory(Long categoryId);

    /**
     * 统计指定状态的商品数量
     */
    long countProductsByStatus(Product.ProductStatus status);

    /**
     * 统计低库存商品数量
     */
    long countLowStockProducts();

    /**
     * 统计缺货商品数量
     */
    long countOutOfStockProducts();

    /**
     * 统计品牌下的商品数量
     */
    long countProductsByBrand(String brand);

    /**
     * 统计活跃商品数量
     */
    long countActiveProducts();

    /**
     * 获取所有品牌
     */
    List<String> getAllBrands();

    /**
     * 获取分类下的所有品牌
     */
    List<String> getBrandsByCategory(Long categoryId);

    /**
     * 获取商品价格范围
     */
    BigDecimal[] getPriceRange();

    /**
     * 获取分类下商品价格范围
     */
    BigDecimal[] getPriceRangeByCategory(Long categoryId);

    /**
     * 检查SKU是否存在
     */
    boolean existsBySku(String sku);

    /**
     * 检查SKU是否存在（排除指定商品）
     */
    boolean existsBySkuAndIdNot(String sku, Long productId);

    /**
     * 验证商品是否存在
     */
    void validateProductExists(Long productId);

    /**
     * 验证SKU唯一性
     */
    void validateSkuUnique(String sku);

    /**
     * 验证SKU唯一性（更新时）
     */
    void validateSkuUniqueForUpdate(String sku, Long productId);
}