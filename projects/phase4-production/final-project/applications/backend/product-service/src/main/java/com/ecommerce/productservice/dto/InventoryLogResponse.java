package com.ecommerce.productservice.dto;

import com.ecommerce.productservice.entity.InventoryLog;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 库存日志响应DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class InventoryLogResponse {

    private Long id;
    private InventoryLog.OperationType operationType;
    private String operationTypeDescription;
    private Integer quantityChange;
    private Integer quantityBefore;
    private Integer quantityAfter;
    private String remarks;
    private String operator;
    private Long operatorId;
    private String relatedOrderNo;
    private String batchNo;
    private Long productId;
    private String productName;
    private String productSku;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // 构造函数
    public InventoryLogResponse() {}

    // 静态工厂方法
    public static InventoryLogResponse fromEntity(InventoryLog inventoryLog) {
        InventoryLogResponse response = new InventoryLogResponse();
        response.setId(inventoryLog.getId());
        response.setOperationType(inventoryLog.getOperationType());
        response.setOperationTypeDescription(inventoryLog.getOperationType().getDescription());
        response.setQuantityChange(inventoryLog.getQuantityChange());
        response.setQuantityBefore(inventoryLog.getQuantityBefore());
        response.setQuantityAfter(inventoryLog.getQuantityAfter());
        response.setRemarks(inventoryLog.getRemarks());
        response.setOperator(inventoryLog.getOperator());
        response.setOperatorId(inventoryLog.getOperatorId());
        response.setRelatedOrderNo(inventoryLog.getRelatedOrderNo());
        response.setBatchNo(inventoryLog.getBatchNo());
        response.setCreatedAt(inventoryLog.getCreatedAt());

        // 设置商品信息
        if (inventoryLog.getProduct() != null) {
            response.setProductId(inventoryLog.getProduct().getId());
            response.setProductName(inventoryLog.getProduct().getName());
            response.setProductSku(inventoryLog.getProduct().getSku());
        }

        return response;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public InventoryLog.OperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(InventoryLog.OperationType operationType) {
        this.operationType = operationType;
    }

    public String getOperationTypeDescription() {
        return operationTypeDescription;
    }

    public void setOperationTypeDescription(String operationTypeDescription) {
        this.operationTypeDescription = operationTypeDescription;
    }

    public Integer getQuantityChange() {
        return quantityChange;
    }

    public void setQuantityChange(Integer quantityChange) {
        this.quantityChange = quantityChange;
    }

    public Integer getQuantityBefore() {
        return quantityBefore;
    }

    public void setQuantityBefore(Integer quantityBefore) {
        this.quantityBefore = quantityBefore;
    }

    public Integer getQuantityAfter() {
        return quantityAfter;
    }

    public void setQuantityAfter(Integer quantityAfter) {
        this.quantityAfter = quantityAfter;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getRelatedOrderNo() {
        return relatedOrderNo;
    }

    public void setRelatedOrderNo(String relatedOrderNo) {
        this.relatedOrderNo = relatedOrderNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "InventoryLogResponse{" +
                "id=" + id +
                ", operationType=" + operationType +
                ", quantityChange=" + quantityChange +
                ", quantityBefore=" + quantityBefore +
                ", quantityAfter=" + quantityAfter +
                ", productSku='" + productSku + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
