server:
  port: 8083
  servlet:
    context-path: /api/orders

spring:
  application:
    name: order-service
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: jdbc:mariadb://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:ecommerce_order}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:ecommerce}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:validate}
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MariaDBDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:2}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:password}
    virtual-host: ${RABBITMQ_VHOST:ecommerce}
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2
  
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    property-naming-strategy: LOWER_CAMEL_CASE
    default-property-inclusion: NON_NULL

# JWT配置
jwt:
  secret: ${JWT_SECRET:ecommerce-order-service-very-long-secret-key-for-hs512-algorithm-that-meets-minimum-512-bits-requirement-2024}
  expiration: ********  # 24小时
  refresh-expiration: *********  # 7天

# 订单配置
order:
  # 订单超时时间（分钟）
  timeout:
    payment: 30  # 支付超时时间
    delivery: 7200  # 发货超时时间（5天）
  # 订单号生成配置
  number:
    prefix: "ORD"
    length: 16
  # 购物车配置
  cart:
    max-items: 100  # 购物车最大商品数量
    expire-days: 30  # 购物车过期天数

# 支付配置
payment:
  # 支付方式
  methods:
    - ALIPAY
    - WECHAT
    - BANK_CARD
  # 支付超时时间（分钟）
  timeout: 30
  # 支付回调配置
  callback:
    retry-times: 3
    retry-interval: 5000

# 日志配置
logging:
  level:
    com.ecommerce.orderservice: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: /app/logs/order-service.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
  metrics:
    export:
      prometheus:
        enabled: true
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: 订单服务 - 云原生电商平台
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
  
  h2:
    console:
      enabled: false

logging:
  level:
    root: INFO
    com.ecommerce.orderservice: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 15

logging:
  level:
    root: WARN
    com.ecommerce.orderservice: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
  
logging:
  level:
    root: WARN
    com.ecommerce.orderservice: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
