package com.ecommerce.orderservice.service;

import com.ecommerce.orderservice.dto.CartItemRequest;
import com.ecommerce.orderservice.dto.ShoppingCartResponse;

/**
 * 购物车服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ShoppingCartService {

    /**
     * 获取用户的购物车
     */
    ShoppingCartResponse getUserCart(Long userId);

    /**
     * 添加商品到购物车
     */
    ShoppingCartResponse addItemToCart(Long userId, CartItemRequest request);

    /**
     * 更新购物车中商品的数量
     */
    ShoppingCartResponse updateCartItemQuantity(Long userId, Long productId, Integer quantity);

    /**
     * 从购物车中移除商品
     */
    ShoppingCartResponse removeItemFromCart(Long userId, Long productId);

    /**
     * 清空购物车
     */
    void clearCart(Long userId);

    /**
     * 获取购物车中商品的总数量
     */
    Integer getCartItemCount(Long userId);

    /**
     * 检查商品是否在购物车中
     */
    boolean isProductInCart(Long userId, Long productId);

    /**
     * 合并购物车（用于用户登录后合并临时购物车）
     */
    ShoppingCartResponse mergeCarts(Long userId, ShoppingCartResponse tempCart);

    /**
     * 验证购物车中商品的有效性（价格、库存等）
     */
    ShoppingCartResponse validateCart(Long userId);

    /**
     * 将购物车转换为订单（清空购物车）
     */
    void convertCartToOrder(Long userId);

    /**
     * 获取购物车的总金额
     */
    java.math.BigDecimal getCartTotalAmount(Long userId);

    /**
     * 批量添加商品到购物车
     */
    ShoppingCartResponse addItemsToCart(Long userId, java.util.List<CartItemRequest> requests);

    /**
     * 批量更新购物车商品数量
     */
    ShoppingCartResponse updateCartItems(Long userId, java.util.Map<Long, Integer> productQuantities);

    /**
     * 批量移除购物车商品
     */
    ShoppingCartResponse removeItemsFromCart(Long userId, java.util.List<Long> productIds);

    /**
     * 检查购物车是否为空
     */
    boolean isCartEmpty(Long userId);

    /**
     * 获取购物车中的商品列表
     */
    java.util.List<com.ecommerce.orderservice.dto.CartItemResponse> getCartItems(Long userId);

    /**
     * 设置购物车过期
     */
    void expireCart(Long userId);

    /**
     * 清理过期的购物车
     */
    void cleanupExpiredCarts();

    /**
     * 获取用户购物车历史
     */
    java.util.List<ShoppingCartResponse> getUserCartHistory(Long userId);

    /**
     * 恢复购物车（从历史记录中）
     */
    ShoppingCartResponse restoreCart(Long userId, Long cartId);
}
