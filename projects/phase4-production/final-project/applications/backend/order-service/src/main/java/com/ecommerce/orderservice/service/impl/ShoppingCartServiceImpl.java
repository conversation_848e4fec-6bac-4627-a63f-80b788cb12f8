package com.ecommerce.orderservice.service.impl;

import com.ecommerce.orderservice.dto.CartItemRequest;
import com.ecommerce.orderservice.dto.CartItemResponse;
import com.ecommerce.orderservice.dto.ShoppingCartResponse;
import com.ecommerce.orderservice.entity.CartItem;
import com.ecommerce.orderservice.entity.ShoppingCart;
import com.ecommerce.orderservice.exception.BadRequestException;
import com.ecommerce.orderservice.exception.ResourceNotFoundException;
import com.ecommerce.orderservice.repository.CartItemRepository;
import com.ecommerce.orderservice.repository.ShoppingCartRepository;
import com.ecommerce.orderservice.service.ShoppingCartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 购物车服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class ShoppingCartServiceImpl implements ShoppingCartService {

    private static final Logger logger = LoggerFactory.getLogger(ShoppingCartServiceImpl.class);

    @Autowired
    private ShoppingCartRepository cartRepository;

    @Autowired
    private CartItemRepository cartItemRepository;

    @Value("${order.cart.max-items:100}")
    private int maxCartItems;

    @Value("${order.cart.expire-days:30}")
    private int cartExpireDays;

    @Override
    @Cacheable(value = "userCarts", key = "#userId")
    public ShoppingCartResponse getUserCart(Long userId) {
        logger.debug("获取用户购物车: userId={}", userId);

        ShoppingCart cart = cartRepository.findActiveCartByUserId(userId)
                .orElseGet(() -> createNewCart(userId));

        return ShoppingCartResponse.fromEntity(cart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse addItemToCart(Long userId, CartItemRequest request) {
        logger.info("添加商品到购物车: userId={}, productId={}, quantity={}", 
                   userId, request.getProductId(), request.getQuantity());

        ShoppingCart cart = getOrCreateCart(userId);

        // 检查购物车商品数量限制
        if (cart.getTotalItems() >= maxCartItems) {
            throw new BadRequestException("购物车商品数量已达上限: " + maxCartItems);
        }

        // 检查商品是否已在购物车中
        CartItem existingItem = cart.findItemByProductId(request.getProductId());
        if (existingItem != null) {
            // 更新数量
            existingItem.setQuantity(existingItem.getQuantity() + request.getQuantity());
            existingItem.recalculateSubtotal();
        } else {
            // TODO: 调用商品服务获取商品信息
            CartItem newItem = new CartItem();
            newItem.setProductId(request.getProductId());
            newItem.setProductName("商品名称"); // 从商品服务获取
            newItem.setUnitPrice(BigDecimal.valueOf(100)); // 从商品服务获取
            newItem.setQuantity(request.getQuantity());
            newItem.recalculateSubtotal();
            
            cart.addItem(newItem);
        }

        cart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("商品添加成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse updateCartItemQuantity(Long userId, Long productId, Integer quantity) {
        logger.info("更新购物车商品数量: userId={}, productId={}, quantity={}", userId, productId, quantity);

        ShoppingCart cart = getActiveCart(userId);
        CartItem item = cart.findItemByProductId(productId);
        
        if (item == null) {
            throw new ResourceNotFoundException("购物车中未找到该商品: " + productId);
        }

        if (quantity <= 0) {
            cart.removeItem(item);
        } else {
            item.setQuantity(quantity);
            item.recalculateSubtotal();
        }

        cart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("商品数量更新成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse removeItemFromCart(Long userId, Long productId) {
        logger.info("从购物车移除商品: userId={}, productId={}", userId, productId);

        ShoppingCart cart = getActiveCart(userId);
        CartItem item = cart.findItemByProductId(productId);
        
        if (item == null) {
            throw new ResourceNotFoundException("购物车中未找到该商品: " + productId);
        }

        cart.removeItem(item);
        cart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("商品移除成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public void clearCart(Long userId) {
        logger.info("清空购物车: userId={}", userId);

        ShoppingCart cart = getActiveCart(userId);
        cart.clearItems();
        cart.recalculateTotal();
        cartRepository.save(cart);
        
        logger.info("购物车清空成功: cartId={}", cart.getId());
    }

    @Override
    public Integer getCartItemCount(Long userId) {
        logger.debug("获取购物车商品数量: userId={}", userId);

        return cartRepository.getActiveCartTotalItems(userId);
    }

    @Override
    public boolean isProductInCart(Long userId, Long productId) {
        logger.debug("检查商品是否在购物车中: userId={}, productId={}", userId, productId);

        return cartItemRepository.existsByUserIdAndProductId(userId, productId);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse mergeCarts(Long userId, ShoppingCartResponse tempCart) {
        logger.info("合并购物车: userId={}", userId);

        ShoppingCart userCart = getOrCreateCart(userId);
        
        if (tempCart != null && tempCart.getItems() != null) {
            for (CartItemResponse tempItem : tempCart.getItems()) {
                CartItem existingItem = userCart.findItemByProductId(tempItem.getProductId());
                if (existingItem != null) {
                    existingItem.setQuantity(existingItem.getQuantity() + tempItem.getQuantity());
                    existingItem.recalculateSubtotal();
                } else {
                    CartItem newItem = new CartItem();
                    newItem.setProductId(tempItem.getProductId());
                    newItem.setProductName(tempItem.getProductName());
                    newItem.setUnitPrice(tempItem.getUnitPrice());
                    newItem.setQuantity(tempItem.getQuantity());
                    newItem.recalculateSubtotal();
                    userCart.addItem(newItem);
                }
            }
        }

        userCart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(userCart);
        
        logger.info("购物车合并成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    public ShoppingCartResponse validateCart(Long userId) {
        logger.info("验证购物车: userId={}", userId);

        ShoppingCart cart = getActiveCart(userId);
        
        // TODO: 调用商品服务验证商品价格和库存
        // 这里应该验证每个商品的价格是否有变化，库存是否充足
        
        return ShoppingCartResponse.fromEntity(cart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public void convertCartToOrder(Long userId) {
        logger.info("将购物车转换为订单: userId={}", userId);

        ShoppingCart cart = getActiveCart(userId);
        cart.setStatus(ShoppingCart.CartStatus.CONVERTED);
        cartRepository.save(cart);
        
        logger.info("购物车转换成功: cartId={}", cart.getId());
    }

    @Override
    public BigDecimal getCartTotalAmount(Long userId) {
        logger.debug("获取购物车总金额: userId={}", userId);

        ShoppingCart cart = cartRepository.findActiveCartByUserId(userId).orElse(null);
        return cart != null ? cart.getTotalAmount() : BigDecimal.ZERO;
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse addItemsToCart(Long userId, List<CartItemRequest> requests) {
        logger.info("批量添加商品到购物车: userId={}, itemCount={}", userId, requests.size());

        ShoppingCart cart = getOrCreateCart(userId);
        
        for (CartItemRequest request : requests) {
            CartItem existingItem = cart.findItemByProductId(request.getProductId());
            if (existingItem != null) {
                existingItem.setQuantity(existingItem.getQuantity() + request.getQuantity());
                existingItem.recalculateSubtotal();
            } else {
                // TODO: 调用商品服务获取商品信息
                CartItem newItem = new CartItem();
                newItem.setProductId(request.getProductId());
                newItem.setProductName("商品名称");
                newItem.setUnitPrice(BigDecimal.valueOf(100));
                newItem.setQuantity(request.getQuantity());
                newItem.recalculateSubtotal();
                cart.addItem(newItem);
            }
        }

        cart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("批量添加成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse updateCartItems(Long userId, Map<Long, Integer> productQuantities) {
        logger.info("批量更新购物车商品: userId={}, itemCount={}", userId, productQuantities.size());

        ShoppingCart cart = getActiveCart(userId);
        
        for (Map.Entry<Long, Integer> entry : productQuantities.entrySet()) {
            Long productId = entry.getKey();
            Integer quantity = entry.getValue();
            
            CartItem item = cart.findItemByProductId(productId);
            if (item != null) {
                if (quantity <= 0) {
                    cart.removeItem(item);
                } else {
                    item.setQuantity(quantity);
                    item.recalculateSubtotal();
                }
            }
        }

        cart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("批量更新成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse removeItemsFromCart(Long userId, List<Long> productIds) {
        logger.info("批量移除购物车商品: userId={}, itemCount={}", userId, productIds.size());

        ShoppingCart cart = getActiveCart(userId);
        
        for (Long productId : productIds) {
            CartItem item = cart.findItemByProductId(productId);
            if (item != null) {
                cart.removeItem(item);
            }
        }

        cart.recalculateTotal();
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("批量移除成功: cartId={}, totalItems={}", savedCart.getId(), savedCart.getTotalItems());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    @Override
    public boolean isCartEmpty(Long userId) {
        logger.debug("检查购物车是否为空: userId={}", userId);

        ShoppingCart cart = cartRepository.findActiveCartByUserId(userId).orElse(null);
        return cart == null || cart.isEmpty();
    }

    @Override
    public List<CartItemResponse> getCartItems(Long userId) {
        logger.debug("获取购物车商品列表: userId={}", userId);

        ShoppingCart cart = cartRepository.findActiveCartByUserId(userId).orElse(null);
        if (cart == null || cart.getItems() == null) {
            return List.of();
        }
        
        return cart.getItems().stream()
                .map(CartItemResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public void expireCart(Long userId) {
        logger.info("设置购物车过期: userId={}", userId);

        ShoppingCart cart = cartRepository.findActiveCartByUserId(userId).orElse(null);
        if (cart != null) {
            cart.setStatus(ShoppingCart.CartStatus.EXPIRED);
            cartRepository.save(cart);
        }
    }

    @Override
    public void cleanupExpiredCarts() {
        logger.info("清理过期购物车");

        LocalDateTime expireTime = LocalDateTime.now().minusDays(cartExpireDays);
        int updatedCount = cartRepository.updateExpiredCartsStatus(
            ShoppingCart.CartStatus.ACTIVE, 
            ShoppingCart.CartStatus.EXPIRED, 
            expireTime
        );
        
        logger.info("过期购物车清理完成: 更新数量={}", updatedCount);
    }

    @Override
    public List<ShoppingCartResponse> getUserCartHistory(Long userId) {
        logger.debug("获取用户购物车历史: userId={}", userId);

        List<ShoppingCart> carts = cartRepository.findByUserIdOrderByCreatedAtDesc(userId);
        return carts.stream()
                .map(ShoppingCartResponse::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "userCarts", key = "#userId")
    public ShoppingCartResponse restoreCart(Long userId, Long cartId) {
        logger.info("恢复购物车: userId={}, cartId={}", userId, cartId);

        ShoppingCart cart = cartRepository.findById(cartId)
                .orElseThrow(() -> new ResourceNotFoundException("购物车不存在: " + cartId));

        if (!cart.getUserId().equals(userId)) {
            throw new BadRequestException("无权限访问该购物车");
        }

        cart.setStatus(ShoppingCart.CartStatus.ACTIVE);
        ShoppingCart savedCart = cartRepository.save(cart);
        
        logger.info("购物车恢复成功: cartId={}", savedCart.getId());
        return ShoppingCartResponse.fromEntity(savedCart);
    }

    // 私有辅助方法
    private ShoppingCart createNewCart(Long userId) {
        ShoppingCart cart = new ShoppingCart(userId);
        return cartRepository.save(cart);
    }

    private ShoppingCart getOrCreateCart(Long userId) {
        return cartRepository.findActiveCartByUserId(userId)
                .orElseGet(() -> createNewCart(userId));
    }

    private ShoppingCart getActiveCart(Long userId) {
        return cartRepository.findActiveCartByUserId(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户没有活跃的购物车: " + userId));
    }
}
