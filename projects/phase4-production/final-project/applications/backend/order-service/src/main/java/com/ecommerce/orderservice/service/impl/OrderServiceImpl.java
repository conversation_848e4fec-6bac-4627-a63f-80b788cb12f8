package com.ecommerce.orderservice.service.impl;

import com.ecommerce.orderservice.dto.OrderRequest;
import com.ecommerce.orderservice.dto.OrderResponse;
import com.ecommerce.orderservice.dto.OrderSearchRequest;
import com.ecommerce.orderservice.entity.Order;
import com.ecommerce.orderservice.entity.OrderItem;
import com.ecommerce.orderservice.entity.OrderStatusHistory;
import com.ecommerce.orderservice.entity.ShoppingCart;
import com.ecommerce.orderservice.exception.BadRequestException;
import com.ecommerce.orderservice.exception.OrderException;
import com.ecommerce.orderservice.exception.ResourceNotFoundException;
import com.ecommerce.orderservice.repository.OrderRepository;
import com.ecommerce.orderservice.repository.OrderStatusHistoryRepository;
import com.ecommerce.orderservice.repository.ShoppingCartRepository;
import com.ecommerce.orderservice.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 订单服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class OrderServiceImpl implements OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderStatusHistoryRepository statusHistoryRepository;

    @Autowired
    private ShoppingCartRepository cartRepository;

    @Value("${order.number.prefix:ORD}")
    private String orderNumberPrefix;

    @Value("${order.timeout.payment:30}")
    private int paymentTimeoutMinutes;

    @Override
    public OrderResponse createOrder(Long userId, OrderRequest request) {
        logger.info("创建订单: userId={}", userId);

        // 验证订单数据
        validateOrderData(request);

        // 检查库存
        if (!checkInventory(request)) {
            throw new OrderException("库存不足，无法创建订单");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNumber(generateOrderNumber());
        order.setUserId(userId);
        order.setStatus(Order.OrderStatus.PENDING);
        order.setPaymentStatus(Order.PaymentStatus.PENDING);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setNotes(request.getNotes());
        order.setCouponCode(request.getCouponCode());

        // 设置收货地址
        order.setShippingName(request.getShippingName());
        order.setShippingPhone(request.getShippingPhone());
        order.setShippingAddress(request.getShippingAddress());
        order.setShippingProvince(request.getShippingProvince());
        order.setShippingCity(request.getShippingCity());
        order.setShippingDistrict(request.getShippingDistrict());
        order.setShippingPostalCode(request.getShippingPostalCode());

        // 添加订单商品项
        for (var itemRequest : request.getItems()) {
            OrderItem item = new OrderItem();
            item.setProductId(itemRequest.getProductId());
            item.setProductName("商品名称"); // TODO: 从商品服务获取
            item.setUnitPrice(itemRequest.getUnitPrice());
            item.setQuantity(itemRequest.getQuantity());
            item.recalculateSubtotal();
            order.addItem(item);
        }

        // 计算订单金额
        order.recalculateTotal();

        // 保存订单
        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, null, Order.OrderStatus.PENDING, "订单创建", "SYSTEM", null);

        // 扣减库存
        reduceInventory(request);

        logger.info("订单创建成功: orderNumber={}, totalAmount={}", 
                   savedOrder.getOrderNumber(), savedOrder.getTotalAmount());

        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse createOrderFromCart(Long userId, OrderRequest request) {
        logger.info("从购物车创建订单: userId={}", userId);

        // 获取用户购物车
        ShoppingCart cart = cartRepository.findActiveCartByUserId(userId)
                .orElseThrow(() -> new BadRequestException("用户没有活跃的购物车"));

        if (cart.isEmpty()) {
            throw new BadRequestException("购物车为空，无法创建订单");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNumber(generateOrderNumber());
        order.setUserId(userId);
        order.setStatus(Order.OrderStatus.PENDING);
        order.setPaymentStatus(Order.PaymentStatus.PENDING);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setNotes(request.getNotes());
        order.setCouponCode(request.getCouponCode());

        // 设置收货地址
        order.setShippingName(request.getShippingName());
        order.setShippingPhone(request.getShippingPhone());
        order.setShippingAddress(request.getShippingAddress());
        order.setShippingProvince(request.getShippingProvince());
        order.setShippingCity(request.getShippingCity());
        order.setShippingDistrict(request.getShippingDistrict());
        order.setShippingPostalCode(request.getShippingPostalCode());

        // 从购物车添加商品项
        for (var cartItem : cart.getItems()) {
            OrderItem item = new OrderItem();
            item.setProductId(cartItem.getProductId());
            item.setProductName(cartItem.getProductName());
            item.setProductSku(cartItem.getProductSku());
            item.setProductImageUrl(cartItem.getProductImageUrl());
            item.setUnitPrice(cartItem.getUnitPrice());
            item.setQuantity(cartItem.getQuantity());
            item.recalculateSubtotal();
            order.addItem(item);
        }

        // 计算订单金额
        order.recalculateTotal();

        // 保存订单
        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, null, Order.OrderStatus.PENDING, "从购物车创建订单", "SYSTEM", null);

        // 将购物车标记为已转换
        cart.setStatus(ShoppingCart.CartStatus.CONVERTED);
        cartRepository.save(cart);

        logger.info("从购物车创建订单成功: orderNumber={}, totalAmount={}", 
                   savedOrder.getOrderNumber(), savedOrder.getTotalAmount());

        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse getOrderById(Long orderId) {
        logger.debug("获取订单详情: orderId={}", orderId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        return OrderResponse.fromEntity(order);
    }

    @Override
    public OrderResponse getOrderByNumber(String orderNumber) {
        logger.debug("根据订单号获取订单: orderNumber={}", orderNumber);

        Order order = orderRepository.findByOrderNumber(orderNumber)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderNumber));

        return OrderResponse.fromEntity(order);
    }

    @Override
    public Page<OrderResponse> getUserOrders(Long userId, Pageable pageable) {
        logger.debug("获取用户订单列表: userId={}", userId);

        Page<Order> orders = orderRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return orders.map(OrderResponse::fromEntity);
    }

    @Override
    public Page<OrderResponse> getUserOrdersByStatus(Long userId, Order.OrderStatus status, Pageable pageable) {
        logger.debug("根据状态获取用户订单: userId={}, status={}", userId, status);

        Page<Order> orders = orderRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, status, pageable);
        return orders.map(OrderResponse::fromEntity);
    }

    @Override
    public Page<OrderResponse> searchOrders(OrderSearchRequest request, Pageable pageable) {
        logger.debug("搜索订单: {}", request);

        // TODO: 实现复杂的订单搜索逻辑
        // 这里简化实现，实际应该使用Specification或自定义查询
        Page<Order> orders = orderRepository.findAll(pageable);
        return orders.map(OrderResponse::fromEntity);
    }

    @Override
    public Page<OrderResponse> getAllOrders(Pageable pageable) {
        logger.debug("获取所有订单: pageable={}", pageable);

        Page<Order> orders = orderRepository.findAll(pageable);
        return orders.map(OrderResponse::fromEntity);
    }

    @Override
    public Page<OrderResponse> getOrdersByStatus(Order.OrderStatus status, Pageable pageable) {
        logger.debug("根据状态获取订单: status={}", status);

        Page<Order> orders = orderRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
        return orders.map(OrderResponse::fromEntity);
    }

    @Override
    public OrderResponse updateOrderStatus(Long orderId, Order.OrderStatus status, String remarks, Long operatorId) {
        logger.info("更新订单状态: orderId={}, status={}, operatorId={}", orderId, status, operatorId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        Order.OrderStatus oldStatus = order.getStatus();
        order.setStatus(status);

        // 根据状态更新相应的时间字段
        LocalDateTime now = LocalDateTime.now();
        switch (status) {
            case PAID:
                order.setPaymentStatus(Order.PaymentStatus.PAID);
                order.setPaymentTime(now);
                break;
            case SHIPPED:
                order.setShippedTime(now);
                break;
            case DELIVERED:
                order.setDeliveredTime(now);
                break;
            case CANCELLED:
                order.setCancelledTime(now);
                break;
        }

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, oldStatus, status, remarks, "ADMIN", operatorId);

        logger.info("订单状态更新成功: orderNumber={}, oldStatus={}, newStatus={}", 
                   savedOrder.getOrderNumber(), oldStatus, status);

        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public String generateOrderNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = ThreadLocalRandom.current().nextInt(1000, 9999);
        return orderNumberPrefix + timestamp + random;
    }

    @Override
    public BigDecimal calculateOrderAmount(OrderRequest request) {
        BigDecimal subtotal = request.getItems().stream()
                .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // TODO: 计算运费、税费、折扣等
        return subtotal;
    }

    @Override
    public void validateOrderData(OrderRequest request) {
        if (request.getItems() == null || request.getItems().isEmpty()) {
            throw new BadRequestException("订单商品不能为空");
        }

        for (var item : request.getItems()) {
            if (item.getQuantity() <= 0) {
                throw new BadRequestException("商品数量必须大于0");
            }
            if (item.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BadRequestException("商品价格必须大于0");
            }
        }
    }

    @Override
    public boolean checkInventory(OrderRequest request) {
        // TODO: 调用商品服务检查库存
        return true;
    }

    @Override
    public void reduceInventory(OrderRequest request) {
        // TODO: 调用商品服务扣减库存
        logger.info("扣减库存: itemCount={}", request.getItems().size());
    }

    @Override
    public void restoreInventory(Long orderId) {
        // TODO: 调用商品服务恢复库存
        logger.info("恢复库存: orderId={}", orderId);
    }

    @Override
    public void sendOrderNotification(Long orderId, String eventType) {
        // TODO: 发送订单通知
        logger.info("发送订单通知: orderId={}, eventType={}", orderId, eventType);
    }

    @Override
    public OrderResponse cancelOrder(Long orderId, String reason, Long userId) {
        logger.info("取消订单: orderId={}, reason={}, userId={}", orderId, reason, userId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        if (!canCancelOrder(orderId, userId)) {
            throw new OrderException("订单无法取消");
        }

        Order.OrderStatus oldStatus = order.getStatus();
        order.setStatus(Order.OrderStatus.CANCELLED);
        order.setCancelledTime(LocalDateTime.now());
        order.setCancelReason(reason);

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, oldStatus, Order.OrderStatus.CANCELLED, reason, "USER", userId);

        // 恢复库存
        restoreInventory(orderId);

        logger.info("订单取消成功: orderNumber={}", savedOrder.getOrderNumber());
        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse cancelOrderByUser(Long orderId, String reason, Long userId) {
        return cancelOrder(orderId, reason, userId);
    }

    @Override
    public OrderResponse cancelOrderByAdmin(Long orderId, String reason, Long adminId) {
        logger.info("管理员取消订单: orderId={}, reason={}, adminId={}", orderId, reason, adminId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        Order.OrderStatus oldStatus = order.getStatus();
        order.setStatus(Order.OrderStatus.CANCELLED);
        order.setCancelledTime(LocalDateTime.now());
        order.setCancelReason(reason);

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, oldStatus, Order.OrderStatus.CANCELLED, reason, "ADMIN", adminId);

        // 恢复库存
        restoreInventory(orderId);

        logger.info("管理员取消订单成功: orderNumber={}", savedOrder.getOrderNumber());
        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse payOrder(Long orderId, Order.PaymentMethod paymentMethod, String transactionId) {
        logger.info("支付订单: orderId={}, paymentMethod={}, transactionId={}", orderId, paymentMethod, transactionId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        if (!canPayOrder(orderId)) {
            throw new OrderException("订单无法支付");
        }

        Order.OrderStatus oldStatus = order.getStatus();
        order.setStatus(Order.OrderStatus.PAID);
        order.setPaymentStatus(Order.PaymentStatus.PAID);
        order.setPaymentMethod(paymentMethod);
        order.setPaymentTime(LocalDateTime.now());

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, oldStatus, Order.OrderStatus.PAID, "订单支付成功", "SYSTEM", null);

        // 发送通知
        sendOrderNotification(orderId, "PAYMENT_SUCCESS");

        logger.info("订单支付成功: orderNumber={}", savedOrder.getOrderNumber());
        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse shipOrder(Long orderId, String trackingNumber, Long operatorId) {
        logger.info("发货: orderId={}, trackingNumber={}, operatorId={}", orderId, trackingNumber, operatorId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        if (!canShipOrder(orderId)) {
            throw new OrderException("订单无法发货");
        }

        Order.OrderStatus oldStatus = order.getStatus();
        order.setStatus(Order.OrderStatus.SHIPPED);
        order.setShippedTime(LocalDateTime.now());

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, oldStatus, Order.OrderStatus.SHIPPED,
                          "订单已发货，快递单号: " + trackingNumber, "ADMIN", operatorId);

        // 发送通知
        sendOrderNotification(orderId, "ORDER_SHIPPED");

        logger.info("订单发货成功: orderNumber={}", savedOrder.getOrderNumber());
        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse confirmDelivery(Long orderId, Long userId) {
        logger.info("确认收货: orderId={}, userId={}", orderId, userId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        if (!canConfirmDelivery(orderId, userId)) {
            throw new OrderException("订单无法确认收货");
        }

        Order.OrderStatus oldStatus = order.getStatus();
        order.setStatus(Order.OrderStatus.DELIVERED);
        order.setDeliveredTime(LocalDateTime.now());

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, oldStatus, Order.OrderStatus.DELIVERED, "用户确认收货", "USER", userId);

        // 发送通知
        sendOrderNotification(orderId, "ORDER_DELIVERED");

        logger.info("确认收货成功: orderNumber={}", savedOrder.getOrderNumber());
        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse requestRefund(Long orderId, String reason, Long userId) {
        logger.info("申请退款: orderId={}, reason={}, userId={}", orderId, reason, userId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        if (!order.getUserId().equals(userId)) {
            throw new BadRequestException("无权限操作该订单");
        }

        if (!order.isPaid()) {
            throw new OrderException("订单未支付，无法申请退款");
        }

        // 记录状态历史
        recordStatusHistory(order, order.getStatus(), order.getStatus(), "用户申请退款: " + reason, "USER", userId);

        // 发送通知
        sendOrderNotification(orderId, "REFUND_REQUESTED");

        logger.info("退款申请成功: orderNumber={}", order.getOrderNumber());
        return OrderResponse.fromEntity(order);
    }

    @Override
    public OrderResponse processRefund(Long orderId, BigDecimal refundAmount, String remarks, Long operatorId) {
        logger.info("处理退款: orderId={}, refundAmount={}, operatorId={}", orderId, refundAmount, operatorId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        order.setStatus(Order.OrderStatus.REFUNDED);
        order.setPaymentStatus(Order.PaymentStatus.REFUNDED);

        Order savedOrder = orderRepository.save(order);

        // 记录状态历史
        recordStatusHistory(savedOrder, Order.OrderStatus.DELIVERED, Order.OrderStatus.REFUNDED,
                          "退款处理完成，金额: " + refundAmount + "，备注: " + remarks, "ADMIN", operatorId);

        // 恢复库存
        restoreInventory(orderId);

        // 发送通知
        sendOrderNotification(orderId, "REFUND_PROCESSED");

        logger.info("退款处理成功: orderNumber={}", savedOrder.getOrderNumber());
        return OrderResponse.fromEntity(savedOrder);
    }

    @Override
    public OrderResponse getOrderDetails(Long orderId) {
        logger.debug("获取订单详情: orderId={}", orderId);

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在: " + orderId));

        return OrderResponse.fromEntity(order);
    }

    @Override
    public boolean canCancelOrder(Long orderId, Long userId) {
        Order order = orderRepository.findById(orderId).orElse(null);
        if (order == null || !order.getUserId().equals(userId)) {
            return false;
        }
        return order.canBeCancelled();
    }

    @Override
    public boolean canPayOrder(Long orderId) {
        Order order = orderRepository.findById(orderId).orElse(null);
        return order != null && order.getStatus() == Order.OrderStatus.PENDING;
    }

    @Override
    public boolean canShipOrder(Long orderId) {
        Order order = orderRepository.findById(orderId).orElse(null);
        return order != null && order.getStatus() == Order.OrderStatus.PAID;
    }

    @Override
    public boolean canConfirmDelivery(Long orderId, Long userId) {
        Order order = orderRepository.findById(orderId).orElse(null);
        if (order == null || !order.getUserId().equals(userId)) {
            return false;
        }
        return order.getStatus() == Order.OrderStatus.SHIPPED;
    }

    @Override
    public Map<String, Object> getOrderStatistics() {
        logger.debug("获取订单统计信息");

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalOrders", orderRepository.count());
        stats.put("pendingOrders", orderRepository.countByStatus(Order.OrderStatus.PENDING));
        stats.put("paidOrders", orderRepository.countByStatus(Order.OrderStatus.PAID));
        stats.put("shippedOrders", orderRepository.countByStatus(Order.OrderStatus.SHIPPED));
        stats.put("deliveredOrders", orderRepository.countByStatus(Order.OrderStatus.DELIVERED));
        stats.put("cancelledOrders", orderRepository.countByStatus(Order.OrderStatus.CANCELLED));

        return stats;
    }

    @Override
    public Map<String, Object> getUserOrderStatistics(Long userId) {
        logger.debug("获取用户订单统计: userId={}", userId);

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalOrders", orderRepository.countByUserId(userId));
        stats.put("pendingOrders", orderRepository.countByUserIdAndStatus(userId, Order.OrderStatus.PENDING));
        stats.put("paidOrders", orderRepository.countByUserIdAndStatus(userId, Order.OrderStatus.PAID));
        stats.put("shippedOrders", orderRepository.countByUserIdAndStatus(userId, Order.OrderStatus.SHIPPED));
        stats.put("deliveredOrders", orderRepository.countByUserIdAndStatus(userId, Order.OrderStatus.DELIVERED));
        stats.put("totalAmount", orderRepository.sumTotalAmountByUserId(userId));

        return stats;
    }

    @Override
    public Map<String, Object> getOrderStatisticsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("获取时间范围内订单统计: startTime={}, endTime={}", startTime, endTime);

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalAmount", orderRepository.sumTotalAmountByDateRange(startTime, endTime));
        // TODO: 添加更多统计信息

        return stats;
    }

    @Override
    public List<Map<String, Object>> getBestSellingProducts(int limit) {
        logger.debug("获取热销商品: limit={}", limit);

        // TODO: 实现热销商品统计
        return List.of();
    }

    @Override
    public List<OrderResponse> getUserPurchaseHistory(Long userId, int limit) {
        logger.debug("获取用户购买历史: userId={}, limit={}", userId, limit);

        // TODO: 实现用户购买历史
        return List.of();
    }

    @Override
    public List<Map<String, Object>> getUserRepeatPurchases(Long userId) {
        logger.debug("获取用户复购商品: userId={}", userId);

        // TODO: 实现用户复购商品统计
        return List.of();
    }

    @Override
    public void handleTimeoutOrders() {
        logger.info("处理超时订单");

        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(paymentTimeoutMinutes);
        List<Order> timeoutOrders = orderRepository.findTimeoutOrders(Order.OrderStatus.PENDING, timeoutTime);

        for (Order order : timeoutOrders) {
            order.setStatus(Order.OrderStatus.CANCELLED);
            order.setCancelledTime(LocalDateTime.now());
            order.setCancelReason("支付超时自动取消");
            orderRepository.save(order);

            // 记录状态历史
            recordStatusHistory(order, Order.OrderStatus.PENDING, Order.OrderStatus.CANCELLED,
                              "支付超时自动取消", "SYSTEM", null);

            // 恢复库存
            restoreInventory(order.getId());
        }

        logger.info("超时订单处理完成: 处理数量={}", timeoutOrders.size());
    }

    @Override
    public void autoConfirmDelivery() {
        logger.info("自动确认收货");

        LocalDateTime autoConfirmTime = LocalDateTime.now().minusDays(7); // 7天后自动确认
        List<Order> orders = orderRepository.findOrdersForAutoConfirm(Order.OrderStatus.SHIPPED, autoConfirmTime);

        for (Order order : orders) {
            order.setStatus(Order.OrderStatus.DELIVERED);
            order.setDeliveredTime(LocalDateTime.now());
            orderRepository.save(order);

            // 记录状态历史
            recordStatusHistory(order, Order.OrderStatus.SHIPPED, Order.OrderStatus.DELIVERED,
                              "系统自动确认收货", "SYSTEM", null);
        }

        logger.info("自动确认收货完成: 处理数量={}", orders.size());
    }

    @Override
    public List<Map<String, Object>> getOrderStatusHistory(Long orderId) {
        logger.debug("获取订单状态历史: orderId={}", orderId);

        // TODO: 实现订单状态历史查询
        return List.of();
    }

    @Override
    public void batchUpdateOrderStatus(List<Long> orderIds, Order.OrderStatus status, String remarks, Long operatorId) {
        logger.info("批量更新订单状态: orderIds={}, status={}, operatorId={}", orderIds, status, operatorId);

        for (Long orderId : orderIds) {
            updateOrderStatus(orderId, status, remarks, operatorId);
        }
    }

    @Override
    public byte[] exportOrders(OrderSearchRequest request) {
        logger.info("导出订单数据: {}", request);

        // TODO: 实现订单数据导出
        return new byte[0];
    }

    @Override
    public Map<String, Object> getOrderReportData(LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("获取订单报表数据: startTime={}, endTime={}", startTime, endTime);

        // TODO: 实现订单报表数据
        return new HashMap<>();
    }

    // 私有辅助方法
    private void recordStatusHistory(Order order, Order.OrderStatus fromStatus, Order.OrderStatus toStatus,
                                   String remarks, String operator, Long operatorId) {
        OrderStatusHistory history = new OrderStatusHistory();
        history.setOrder(order);
        history.setFromStatus(fromStatus);
        history.setToStatus(toStatus);
        history.setRemarks(remarks);
        history.setOperator(operator);
        history.setOperatorId(operatorId);

        statusHistoryRepository.save(history);
    }
}
