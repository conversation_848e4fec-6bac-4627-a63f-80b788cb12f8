package com.ecommerce.orderservice.repository;

import com.ecommerce.orderservice.entity.ShoppingCart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 购物车数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface ShoppingCartRepository extends JpaRepository<ShoppingCart, Long> {

    /**
     * 根据用户ID查找活跃的购物车
     */
    Optional<ShoppingCart> findByUserIdAndStatus(Long userId, ShoppingCart.CartStatus status);

    /**
     * 根据用户ID查找活跃的购物车
     */
    default Optional<ShoppingCart> findActiveCartByUserId(Long userId) {
        return findByUserIdAndStatus(userId, ShoppingCart.CartStatus.ACTIVE);
    }

    /**
     * 根据用户ID查找所有购物车
     */
    List<ShoppingCart> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据状态查找购物车
     */
    List<ShoppingCart> findByStatus(ShoppingCart.CartStatus status);

    /**
     * 查找过期的购物车
     */
    @Query("SELECT c FROM ShoppingCart c WHERE c.status = :status AND c.updatedAt < :expireTime")
    List<ShoppingCart> findExpiredCarts(@Param("status") ShoppingCart.CartStatus status, 
                                       @Param("expireTime") LocalDateTime expireTime);

    /**
     * 查找空的购物车
     */
    @Query("SELECT c FROM ShoppingCart c WHERE c.totalItems = 0 AND c.status = :status")
    List<ShoppingCart> findEmptyCarts(@Param("status") ShoppingCart.CartStatus status);

    /**
     * 统计用户的购物车数量
     */
    long countByUserId(Long userId);

    /**
     * 统计活跃购物车数量
     */
    long countByStatus(ShoppingCart.CartStatus status);

    /**
     * 批量更新购物车状态
     */
    @Modifying
    @Query("UPDATE ShoppingCart c SET c.status = :newStatus WHERE c.status = :oldStatus AND c.updatedAt < :expireTime")
    int updateExpiredCartsStatus(@Param("oldStatus") ShoppingCart.CartStatus oldStatus,
                                @Param("newStatus") ShoppingCart.CartStatus newStatus,
                                @Param("expireTime") LocalDateTime expireTime);

    /**
     * 删除过期的购物车
     */
    @Modifying
    @Query("DELETE FROM ShoppingCart c WHERE c.status = :status AND c.updatedAt < :expireTime")
    int deleteExpiredCarts(@Param("status") ShoppingCart.CartStatus status, 
                          @Param("expireTime") LocalDateTime expireTime);

    /**
     * 检查用户是否有活跃的购物车
     */
    boolean existsByUserIdAndStatus(Long userId, ShoppingCart.CartStatus status);

    /**
     * 检查用户是否有活跃的购物车
     */
    default boolean hasActiveCart(Long userId) {
        return existsByUserIdAndStatus(userId, ShoppingCart.CartStatus.ACTIVE);
    }

    /**
     * 获取用户购物车的商品总数
     */
    @Query("SELECT COALESCE(SUM(c.totalItems), 0) FROM ShoppingCart c WHERE c.userId = :userId AND c.status = :status")
    Integer getTotalItemsByUserIdAndStatus(@Param("userId") Long userId, 
                                          @Param("status") ShoppingCart.CartStatus status);

    /**
     * 获取用户活跃购物车的商品总数
     */
    default Integer getActiveCartTotalItems(Long userId) {
        return getTotalItemsByUserIdAndStatus(userId, ShoppingCart.CartStatus.ACTIVE);
    }

    /**
     * 查找需要清理的购物车（长时间未更新）
     */
    @Query("SELECT c FROM ShoppingCart c WHERE c.updatedAt < :cleanupTime ORDER BY c.updatedAt ASC")
    List<ShoppingCart> findCartsForCleanup(@Param("cleanupTime") LocalDateTime cleanupTime);

    /**
     * 统计各状态的购物车数量
     */
    @Query("SELECT c.status, COUNT(c) FROM ShoppingCart c GROUP BY c.status")
    List<Object[]> countByStatus();

    /**
     * 查找最近创建的购物车
     */
    @Query("SELECT c FROM ShoppingCart c WHERE c.createdAt >= :startTime ORDER BY c.createdAt DESC")
    List<ShoppingCart> findRecentCarts(@Param("startTime") LocalDateTime startTime);

    /**
     * 查找用户最近的购物车
     */
    @Query("SELECT c FROM ShoppingCart c WHERE c.userId = :userId ORDER BY c.updatedAt DESC")
    List<ShoppingCart> findUserRecentCarts(@Param("userId") Long userId);
}
