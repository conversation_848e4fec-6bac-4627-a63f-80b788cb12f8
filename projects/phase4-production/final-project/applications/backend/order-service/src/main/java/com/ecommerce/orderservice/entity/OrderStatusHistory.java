package com.ecommerce.orderservice.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 订单状态历史记录实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Entity
@Table(name = "order_status_history", indexes = {
    @Index(name = "idx_order_status_order", columnList = "order_id"),
    @Index(name = "idx_order_status_created", columnList = "created_at")
})
@EntityListeners(AuditingEntityListener.class)
public class OrderStatusHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "from_status", length = 20)
    private Order.OrderStatus fromStatus;

    @NotNull(message = "目标状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "to_status", nullable = false, length = 20)
    private Order.OrderStatus toStatus;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remarks", length = 500)
    private String remarks;

    @Size(max = 100, message = "操作人长度不能超过100个字符")
    @Column(name = "operator", length = 100)
    private String operator;

    @Column(name = "operator_id")
    private Long operatorId;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private Order order;

    // 构造函数
    public OrderStatusHistory() {}

    public OrderStatusHistory(Order.OrderStatus fromStatus, Order.OrderStatus toStatus, 
                             String remarks, String operator, Long operatorId) {
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.remarks = remarks;
        this.operator = operator;
        this.operatorId = operatorId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Order.OrderStatus getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(Order.OrderStatus fromStatus) {
        this.fromStatus = fromStatus;
    }

    public Order.OrderStatus getToStatus() {
        return toStatus;
    }

    public void setToStatus(Order.OrderStatus toStatus) {
        this.toStatus = toStatus;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        OrderStatusHistory that = (OrderStatusHistory) o;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "OrderStatusHistory{" +
                "id=" + id +
                ", fromStatus=" + fromStatus +
                ", toStatus=" + toStatus +
                ", operator='" + operator + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
