package com.ecommerce.orderservice.repository;

import com.ecommerce.orderservice.entity.Order;
import com.ecommerce.orderservice.entity.OrderStatusHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单状态历史数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface OrderStatusHistoryRepository extends JpaRepository<OrderStatusHistory, Long> {

    /**
     * 根据订单ID查找状态历史
     */
    List<OrderStatusHistory> findByOrderIdOrderByCreatedAtDesc(Long orderId);

    /**
     * 根据订单ID查找状态历史（分页）
     */
    Page<OrderStatusHistory> findByOrderIdOrderByCreatedAtDesc(Long orderId, Pageable pageable);

    /**
     * 根据目标状态查找状态历史
     */
    List<OrderStatusHistory> findByToStatusOrderByCreatedAtDesc(Order.OrderStatus toStatus);

    /**
     * 根据操作人查找状态历史
     */
    List<OrderStatusHistory> findByOperatorIdOrderByCreatedAtDesc(Long operatorId);

    /**
     * 根据操作人查找状态历史（分页）
     */
    Page<OrderStatusHistory> findByOperatorIdOrderByCreatedAtDesc(Long operatorId, Pageable pageable);

    /**
     * 根据时间范围查找状态历史
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.createdAt BETWEEN :startTime AND :endTime ORDER BY osh.createdAt DESC")
    Page<OrderStatusHistory> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime,
                                                   Pageable pageable);

    /**
     * 根据订单和时间范围查找状态历史
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.order.id = :orderId AND osh.createdAt BETWEEN :startTime AND :endTime ORDER BY osh.createdAt DESC")
    List<OrderStatusHistory> findByOrderIdAndCreatedAtBetween(@Param("orderId") Long orderId,
                                                             @Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查找订单的最新状态历史
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.order.id = :orderId ORDER BY osh.createdAt DESC LIMIT 1")
    OrderStatusHistory findLatestByOrderId(@Param("orderId") Long orderId);

    /**
     * 查找订单的第一个状态历史
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.order.id = :orderId ORDER BY osh.createdAt ASC LIMIT 1")
    OrderStatusHistory findFirstByOrderId(@Param("orderId") Long orderId);

    /**
     * 统计订单状态变更次数
     */
    long countByOrderId(Long orderId);

    /**
     * 统计操作人的操作次数
     */
    long countByOperatorId(Long operatorId);

    /**
     * 统计各状态的变更次数
     */
    @Query("SELECT osh.toStatus, COUNT(osh) FROM OrderStatusHistory osh GROUP BY osh.toStatus")
    List<Object[]> countByToStatus();

    /**
     * 统计状态转换情况
     */
    @Query("SELECT osh.fromStatus, osh.toStatus, COUNT(osh) FROM OrderStatusHistory osh " +
           "WHERE osh.fromStatus IS NOT NULL GROUP BY osh.fromStatus, osh.toStatus")
    List<Object[]> countByStatusTransition();

    /**
     * 查找特定状态转换的历史记录
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.fromStatus = :fromStatus AND osh.toStatus = :toStatus ORDER BY osh.createdAt DESC")
    List<OrderStatusHistory> findByStatusTransition(@Param("fromStatus") Order.OrderStatus fromStatus,
                                                   @Param("toStatus") Order.OrderStatus toStatus);

    /**
     * 统计时间范围内的状态变更
     */
    @Query("SELECT osh.toStatus, COUNT(osh) FROM OrderStatusHistory osh " +
           "WHERE osh.createdAt BETWEEN :startTime AND :endTime GROUP BY osh.toStatus")
    List<Object[]> countByToStatusAndDateRange(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查找操作人在时间范围内的操作记录
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.operatorId = :operatorId " +
           "AND osh.createdAt BETWEEN :startTime AND :endTime ORDER BY osh.createdAt DESC")
    List<OrderStatusHistory> findByOperatorIdAndDateRange(@Param("operatorId") Long operatorId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查找订单状态变更的平均时间
     */
    @Query("SELECT AVG(TIMESTAMPDIFF(MINUTE, osh1.createdAt, osh2.createdAt)) " +
           "FROM OrderStatusHistory osh1 JOIN OrderStatusHistory osh2 ON osh1.order.id = osh2.order.id " +
           "WHERE osh1.toStatus = :fromStatus AND osh2.toStatus = :toStatus " +
           "AND osh2.createdAt > osh1.createdAt")
    Double findAverageTransitionTime(@Param("fromStatus") Order.OrderStatus fromStatus,
                                    @Param("toStatus") Order.OrderStatus toStatus);

    /**
     * 查找处理时间最长的订单
     */
    @Query("SELECT osh.order.id, TIMESTAMPDIFF(MINUTE, MIN(osh.createdAt), MAX(osh.createdAt)) as processingTime " +
           "FROM OrderStatusHistory osh GROUP BY osh.order.id ORDER BY processingTime DESC")
    List<Object[]> findOrdersWithLongestProcessingTime();

    /**
     * 查找快速处理的订单
     */
    @Query("SELECT osh.order.id, TIMESTAMPDIFF(MINUTE, MIN(osh.createdAt), MAX(osh.createdAt)) as processingTime " +
           "FROM OrderStatusHistory osh GROUP BY osh.order.id " +
           "HAVING TIMESTAMPDIFF(MINUTE, MIN(osh.createdAt), MAX(osh.createdAt)) <= :maxProcessingMinutes ORDER BY processingTime ASC")
    List<Object[]> findQuicklyProcessedOrders(@Param("maxProcessingMinutes") Long maxProcessingMinutes);

    /**
     * 查找异常状态变更（跳过某些状态）
     */
    @Query("SELECT osh FROM OrderStatusHistory osh WHERE osh.order.id IN " +
           "(SELECT osh2.order.id FROM OrderStatusHistory osh2 " +
           "WHERE osh2.fromStatus = 'PENDING' AND osh2.toStatus = 'SHIPPED')")
    List<OrderStatusHistory> findAbnormalStatusTransitions();

    /**
     * 统计每日状态变更数量
     */
    @Query("SELECT DATE(osh.createdAt) as date, osh.toStatus, COUNT(osh) " +
           "FROM OrderStatusHistory osh " +
           "WHERE osh.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(osh.createdAt), osh.toStatus " +
           "ORDER BY date DESC")
    List<Object[]> findDailyStatusChanges(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查找最活跃的操作人
     */
    @Query("SELECT osh.operatorId, osh.operator, COUNT(osh) as operationCount " +
           "FROM OrderStatusHistory osh WHERE osh.operatorId IS NOT NULL " +
           "GROUP BY osh.operatorId, osh.operator ORDER BY operationCount DESC")
    List<Object[]> findMostActiveOperators();

    /**
     * 查找用户订单的状态历史
     */
    @Query("SELECT osh FROM OrderStatusHistory osh JOIN osh.order o " +
           "WHERE o.userId = :userId ORDER BY osh.createdAt DESC")
    List<OrderStatusHistory> findByUserId(@Param("userId") Long userId);

    /**
     * 检查订单是否有特定的状态变更
     */
    @Query("SELECT COUNT(osh) > 0 FROM OrderStatusHistory osh " +
           "WHERE osh.order.id = :orderId AND osh.toStatus = :status")
    boolean existsByOrderIdAndToStatus(@Param("orderId") Long orderId, @Param("status") Order.OrderStatus status);
}
