package com.ecommerce.orderservice.repository;

import com.ecommerce.orderservice.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单商品项数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, Long> {

    /**
     * 根据订单ID查找所有商品项
     */
    List<OrderItem> findByOrderIdOrderByCreatedAtDesc(Long orderId);

    /**
     * 根据商品ID查找所有订单项
     */
    List<OrderItem> findByProductIdOrderByCreatedAtDesc(Long productId);

    /**
     * 根据订单ID和商品ID查找订单项
     */
    List<OrderItem> findByOrderIdAndProductId(Long orderId, Long productId);

    /**
     * 统计订单中的商品项数量
     */
    long countByOrderId(Long orderId);

    /**
     * 统计商品的销售次数
     */
    long countByProductId(Long productId);

    /**
     * 计算商品的总销售数量
     */
    @Query("SELECT COALESCE(SUM(oi.quantity), 0) FROM OrderItem oi WHERE oi.productId = :productId")
    Integer sumQuantityByProductId(@Param("productId") Long productId);

    /**
     * 计算商品的总销售金额
     */
    @Query("SELECT COALESCE(SUM(oi.subtotal), 0) FROM OrderItem oi WHERE oi.productId = :productId")
    BigDecimal sumSubtotalByProductId(@Param("productId") Long productId);

    /**
     * 计算订单的商品总金额
     */
    @Query("SELECT COALESCE(SUM(oi.subtotal), 0) FROM OrderItem oi WHERE oi.order.id = :orderId")
    BigDecimal sumSubtotalByOrderId(@Param("orderId") Long orderId);

    /**
     * 查找用户购买过的所有商品项
     */
    @Query("SELECT oi FROM OrderItem oi JOIN oi.order o WHERE o.userId = :userId ORDER BY oi.createdAt DESC")
    List<OrderItem> findByUserId(@Param("userId") Long userId);

    /**
     * 查找用户购买过的特定商品的订单项
     */
    @Query("SELECT oi FROM OrderItem oi JOIN oi.order o WHERE o.userId = :userId AND oi.productId = :productId ORDER BY oi.createdAt DESC")
    List<OrderItem> findByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * 统计用户购买特定商品的总数量
     */
    @Query("SELECT COALESCE(SUM(oi.quantity), 0) FROM OrderItem oi JOIN oi.order o WHERE o.userId = :userId AND oi.productId = :productId")
    Integer sumQuantityByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * 查找热销商品（按销售数量排序）
     */
    @Query("SELECT oi.productId, oi.productName, SUM(oi.quantity) as totalSold FROM OrderItem oi " +
           "GROUP BY oi.productId, oi.productName ORDER BY totalSold DESC")
    List<Object[]> findBestSellingProducts();

    /**
     * 查找指定时间范围内的热销商品
     */
    @Query("SELECT oi.productId, oi.productName, SUM(oi.quantity) as totalSold FROM OrderItem oi " +
           "WHERE oi.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY oi.productId, oi.productName ORDER BY totalSold DESC")
    List<Object[]> findBestSellingProductsByDateRange(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查找高价值商品项（按单价排序）
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.unitPrice >= :minPrice ORDER BY oi.unitPrice DESC")
    List<OrderItem> findHighValueItems(@Param("minPrice") BigDecimal minPrice);

    /**
     * 查找大批量购买的商品项
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.quantity >= :minQuantity ORDER BY oi.quantity DESC")
    List<OrderItem> findBulkPurchaseItems(@Param("minQuantity") Integer minQuantity);

    /**
     * 统计商品在不同价格区间的销售情况
     */
    @Query("SELECT " +
           "CASE " +
           "WHEN oi.unitPrice < 100 THEN 'LOW' " +
           "WHEN oi.unitPrice < 500 THEN 'MEDIUM' " +
           "ELSE 'HIGH' " +
           "END as priceRange, " +
           "COUNT(oi) as count, " +
           "SUM(oi.quantity) as totalQuantity " +
           "FROM OrderItem oi " +
           "GROUP BY " +
           "CASE " +
           "WHEN oi.unitPrice < 100 THEN 'LOW' " +
           "WHEN oi.unitPrice < 500 THEN 'MEDIUM' " +
           "ELSE 'HIGH' " +
           "END")
    List<Object[]> findSalesByPriceRange();

    /**
     * 查找用户的复购商品
     */
    @Query("SELECT oi.productId, COUNT(DISTINCT oi.order.id) as orderCount FROM OrderItem oi " +
           "JOIN oi.order o WHERE o.userId = :userId " +
           "GROUP BY oi.productId HAVING COUNT(DISTINCT oi.order.id) > 1 " +
           "ORDER BY orderCount DESC")
    List<Object[]> findRepeatPurchasesByUserId(@Param("userId") Long userId);

    /**
     * 查找商品的平均销售价格
     */
    @Query("SELECT oi.productId, AVG(oi.unitPrice) as avgPrice FROM OrderItem oi " +
           "WHERE oi.productId = :productId GROUP BY oi.productId")
    BigDecimal findAveragePriceByProductId(@Param("productId") Long productId);

    /**
     * 查找商品的价格变化趋势
     */
    @Query("SELECT oi.unitPrice, oi.createdAt FROM OrderItem oi " +
           "WHERE oi.productId = :productId ORDER BY oi.createdAt ASC")
    List<Object[]> findPriceTrendByProductId(@Param("productId") Long productId);

    /**
     * 统计每月的销售数据
     */
    @Query("SELECT " +
           "YEAR(oi.createdAt) as year, " +
           "MONTH(oi.createdAt) as month, " +
           "COUNT(oi) as itemCount, " +
           "SUM(oi.quantity) as totalQuantity, " +
           "SUM(oi.subtotal) as totalAmount " +
           "FROM OrderItem oi " +
           "WHERE oi.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY YEAR(oi.createdAt), MONTH(oi.createdAt) " +
           "ORDER BY year DESC, month DESC")
    List<Object[]> findMonthlySalesData(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查找商品的关联销售（经常一起购买的商品）
     */
    @Query("SELECT oi2.productId, COUNT(*) as frequency FROM OrderItem oi1 " +
           "JOIN OrderItem oi2 ON oi1.order.id = oi2.order.id " +
           "WHERE oi1.productId = :productId AND oi2.productId != :productId " +
           "GROUP BY oi2.productId ORDER BY frequency DESC")
    List<Object[]> findFrequentlyBoughtTogether(@Param("productId") Long productId);

    /**
     * 查找用户最近购买的商品
     */
    @Query("SELECT oi FROM OrderItem oi JOIN oi.order o " +
           "WHERE o.userId = :userId ORDER BY oi.createdAt DESC")
    List<OrderItem> findRecentPurchasesByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否购买过某商品
     */
    @Query("SELECT COUNT(oi) > 0 FROM OrderItem oi JOIN oi.order o " +
           "WHERE o.userId = :userId AND oi.productId = :productId")
    boolean existsByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);
}
