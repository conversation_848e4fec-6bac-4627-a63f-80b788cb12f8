package com.ecommerce.orderservice.controller;

import com.ecommerce.orderservice.dto.ApiResponse;
import com.ecommerce.orderservice.dto.CartItemRequest;
import com.ecommerce.orderservice.dto.CartItemResponse;
import com.ecommerce.orderservice.dto.ShoppingCartResponse;
import com.ecommerce.orderservice.security.UserPrincipal;
import com.ecommerce.orderservice.service.ShoppingCartService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 购物车控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/orders/cart")
public class ShoppingCartController {

    private static final Logger logger = LoggerFactory.getLogger(ShoppingCartController.class);

    @Autowired
    private ShoppingCartService cartService;

    /**
     * 获取用户购物车
     */
    @GetMapping
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> getUserCart(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户购物车: userId={}", currentUser.getId());
        
        ShoppingCartResponse response = cartService.getUserCart(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 添加商品到购物车
     */
    @PostMapping("/items")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> addItemToCart(
            @Valid @RequestBody CartItemRequest request,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("添加商品到购物车: userId={}, productId={}, quantity={}", 
                   currentUser.getId(), request.getProductId(), request.getQuantity());
        
        ShoppingCartResponse response = cartService.addItemToCart(currentUser.getId(), request);
        return ResponseEntity.ok(ApiResponse.success("商品添加成功", response));
    }

    /**
     * 更新购物车商品数量
     */
    @PutMapping("/items/{productId}")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> updateCartItemQuantity(
            @PathVariable Long productId,
            @RequestParam Integer quantity,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("更新购物车商品数量: userId={}, productId={}, quantity={}", 
                   currentUser.getId(), productId, quantity);
        
        ShoppingCartResponse response = cartService.updateCartItemQuantity(
            currentUser.getId(), productId, quantity);
        return ResponseEntity.ok(ApiResponse.success("数量更新成功", response));
    }

    /**
     * 从购物车移除商品
     */
    @DeleteMapping("/items/{productId}")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> removeItemFromCart(
            @PathVariable Long productId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("从购物车移除商品: userId={}, productId={}", currentUser.getId(), productId);
        
        ShoppingCartResponse response = cartService.removeItemFromCart(currentUser.getId(), productId);
        return ResponseEntity.ok(ApiResponse.success("商品移除成功", response));
    }

    /**
     * 清空购物车
     */
    @DeleteMapping
    public ResponseEntity<ApiResponse<Void>> clearCart(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("清空购物车: userId={}", currentUser.getId());
        
        cartService.clearCart(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("购物车清空成功"));
    }

    /**
     * 获取购物车商品数量
     */
    @GetMapping("/count")
    public ResponseEntity<ApiResponse<Integer>> getCartItemCount(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取购物车商品数量: userId={}", currentUser.getId());
        
        Integer count = cartService.getCartItemCount(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    /**
     * 检查商品是否在购物车中
     */
    @GetMapping("/items/{productId}/exists")
    public ResponseEntity<ApiResponse<Boolean>> isProductInCart(
            @PathVariable Long productId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("检查商品是否在购物车中: userId={}, productId={}", currentUser.getId(), productId);
        
        boolean exists = cartService.isProductInCart(currentUser.getId(), productId);
        return ResponseEntity.ok(ApiResponse.success(exists));
    }

    /**
     * 获取购物车总金额
     */
    @GetMapping("/total")
    public ResponseEntity<ApiResponse<BigDecimal>> getCartTotalAmount(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取购物车总金额: userId={}", currentUser.getId());
        
        BigDecimal totalAmount = cartService.getCartTotalAmount(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(totalAmount));
    }

    /**
     * 批量添加商品到购物车
     */
    @PostMapping("/items/batch")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> addItemsToCart(
            @Valid @RequestBody List<CartItemRequest> requests,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("批量添加商品到购物车: userId={}, itemCount={}", 
                   currentUser.getId(), requests.size());
        
        ShoppingCartResponse response = cartService.addItemsToCart(currentUser.getId(), requests);
        return ResponseEntity.ok(ApiResponse.success("商品批量添加成功", response));
    }

    /**
     * 批量更新购物车商品数量
     */
    @PutMapping("/items/batch")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> updateCartItems(
            @RequestBody Map<Long, Integer> productQuantities,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("批量更新购物车商品: userId={}, itemCount={}", 
                   currentUser.getId(), productQuantities.size());
        
        ShoppingCartResponse response = cartService.updateCartItems(currentUser.getId(), productQuantities);
        return ResponseEntity.ok(ApiResponse.success("商品批量更新成功", response));
    }

    /**
     * 批量移除购物车商品
     */
    @DeleteMapping("/items/batch")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> removeItemsFromCart(
            @RequestBody List<Long> productIds,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("批量移除购物车商品: userId={}, itemCount={}", 
                   currentUser.getId(), productIds.size());
        
        ShoppingCartResponse response = cartService.removeItemsFromCart(currentUser.getId(), productIds);
        return ResponseEntity.ok(ApiResponse.success("商品批量移除成功", response));
    }

    /**
     * 检查购物车是否为空
     */
    @GetMapping("/empty")
    public ResponseEntity<ApiResponse<Boolean>> isCartEmpty(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("检查购物车是否为空: userId={}", currentUser.getId());
        
        boolean isEmpty = cartService.isCartEmpty(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(isEmpty));
    }

    /**
     * 获取购物车商品列表
     */
    @GetMapping("/items")
    public ResponseEntity<ApiResponse<List<CartItemResponse>>> getCartItems(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取购物车商品列表: userId={}", currentUser.getId());
        
        List<CartItemResponse> items = cartService.getCartItems(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(items));
    }

    /**
     * 验证购物车
     */
    @PostMapping("/validate")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> validateCart(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("验证购物车: userId={}", currentUser.getId());
        
        ShoppingCartResponse response = cartService.validateCart(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("购物车验证完成", response));
    }

    /**
     * 合并购物车
     */
    @PostMapping("/merge")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> mergeCarts(
            @RequestBody ShoppingCartResponse tempCart,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("合并购物车: userId={}", currentUser.getId());
        
        ShoppingCartResponse response = cartService.mergeCarts(currentUser.getId(), tempCart);
        return ResponseEntity.ok(ApiResponse.success("购物车合并成功", response));
    }

    /**
     * 获取用户购物车历史
     */
    @GetMapping("/history")
    public ResponseEntity<ApiResponse<List<ShoppingCartResponse>>> getUserCartHistory(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户购物车历史: userId={}", currentUser.getId());
        
        List<ShoppingCartResponse> history = cartService.getUserCartHistory(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(history));
    }

    /**
     * 恢复购物车
     */
    @PostMapping("/{cartId}/restore")
    public ResponseEntity<ApiResponse<ShoppingCartResponse>> restoreCart(
            @PathVariable Long cartId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("恢复购物车: userId={}, cartId={}", currentUser.getId(), cartId);
        
        ShoppingCartResponse response = cartService.restoreCart(currentUser.getId(), cartId);
        return ResponseEntity.ok(ApiResponse.success("购物车恢复成功", response));
    }
}
