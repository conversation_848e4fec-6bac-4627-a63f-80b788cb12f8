package com.ecommerce.orderservice.controller;

import com.ecommerce.orderservice.dto.ApiResponse;
import com.ecommerce.orderservice.dto.OrderRequest;
import com.ecommerce.orderservice.dto.OrderResponse;
import com.ecommerce.orderservice.dto.OrderSearchRequest;
import com.ecommerce.orderservice.entity.Order;
import com.ecommerce.orderservice.security.UserPrincipal;
import com.ecommerce.orderservice.service.OrderService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/orders")
public class OrderController {

    private static final Logger logger = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    private OrderService orderService;

    /**
     * 创建订单
     */
    @PostMapping
    public ResponseEntity<ApiResponse<OrderResponse>> createOrder(
            @Valid @RequestBody OrderRequest request,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("创建订单: userId={}", currentUser.getId());
        
        OrderResponse response = orderService.createOrder(currentUser.getId(), request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("订单创建成功", response));
    }

    /**
     * 从购物车创建订单
     */
    @PostMapping("/from-cart")
    public ResponseEntity<ApiResponse<OrderResponse>> createOrderFromCart(
            @Valid @RequestBody OrderRequest request,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("从购物车创建订单: userId={}", currentUser.getId());
        
        OrderResponse response = orderService.createOrderFromCart(currentUser.getId(), request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("订单创建成功", response));
    }

    /**
     * 根据ID获取订单
     */
    @GetMapping("/{orderId}")
    public ResponseEntity<ApiResponse<OrderResponse>> getOrderById(
            @PathVariable Long orderId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取订单详情: orderId={}, userId={}", orderId, currentUser.getId());
        
        OrderResponse response = orderService.getOrderById(orderId);
        
        // 检查权限：只有订单所有者或管理员可以查看
        if (!response.getUserId().equals(currentUser.getId()) && 
            !currentUser.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"))) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(403, "无权限访问该订单"));
        }
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 根据订单号获取订单
     */
    @GetMapping("/number/{orderNumber}")
    public ResponseEntity<ApiResponse<OrderResponse>> getOrderByNumber(
            @PathVariable String orderNumber,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("根据订单号获取订单: orderNumber={}, userId={}", orderNumber, currentUser.getId());
        
        OrderResponse response = orderService.getOrderByNumber(orderNumber);
        
        // 检查权限
        if (!response.getUserId().equals(currentUser.getId()) && 
            !currentUser.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"))) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(403, "无权限访问该订单"));
        }
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/user")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> getUserOrders(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(required = false) Order.OrderStatus status,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户订单列表: userId={}, status={}", currentUser.getId(), status);
        
        Page<OrderResponse> response = status != null 
            ? orderService.getUserOrdersByStatus(currentUser.getId(), status, pageable)
            : orderService.getUserOrders(currentUser.getId(), pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 搜索订单
     */
    @PostMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> searchOrders(
            @RequestBody OrderSearchRequest request,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("搜索订单: {}", request);
        
        Page<OrderResponse> response = orderService.searchOrders(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 获取所有订单（管理员）
     */
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<OrderResponse>>> getAllOrders(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(required = false) Order.OrderStatus status) {
        logger.debug("获取所有订单: status={}", status);
        
        Page<OrderResponse> response = status != null 
            ? orderService.getOrdersByStatus(status, pageable)
            : orderService.getAllOrders(pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 更新订单状态（管理员）
     */
    @PutMapping("/{orderId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<OrderResponse>> updateOrderStatus(
            @PathVariable Long orderId,
            @RequestParam Order.OrderStatus status,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("更新订单状态: orderId={}, status={}, adminId={}", orderId, status, currentUser.getId());
        
        OrderResponse response = orderService.updateOrderStatus(orderId, status, remarks, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("订单状态更新成功", response));
    }

    /**
     * 取消订单
     */
    @PutMapping("/{orderId}/cancel")
    public ResponseEntity<ApiResponse<OrderResponse>> cancelOrder(
            @PathVariable Long orderId,
            @RequestParam(required = false) String reason,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("取消订单: orderId={}, reason={}, userId={}", orderId, reason, currentUser.getId());
        
        OrderResponse response = orderService.cancelOrderByUser(orderId, reason, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("订单取消成功", response));
    }

    /**
     * 管理员取消订单
     */
    @PutMapping("/{orderId}/admin/cancel")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<OrderResponse>> cancelOrderByAdmin(
            @PathVariable Long orderId,
            @RequestParam(required = false) String reason,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("管理员取消订单: orderId={}, reason={}, adminId={}", orderId, reason, currentUser.getId());
        
        OrderResponse response = orderService.cancelOrderByAdmin(orderId, reason, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("订单取消成功", response));
    }

    /**
     * 支付订单
     */
    @PostMapping("/{orderId}/payment")
    public ResponseEntity<ApiResponse<OrderResponse>> payOrder(
            @PathVariable Long orderId,
            @RequestParam Order.PaymentMethod paymentMethod,
            @RequestParam String transactionId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("支付订单: orderId={}, paymentMethod={}, userId={}", orderId, paymentMethod, currentUser.getId());
        
        // 验证订单所有权
        OrderResponse order = orderService.getOrderById(orderId);
        if (!order.getUserId().equals(currentUser.getId())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(403, "无权限操作该订单"));
        }
        
        OrderResponse response = orderService.payOrder(orderId, paymentMethod, transactionId);
        return ResponseEntity.ok(ApiResponse.success("订单支付成功", response));
    }

    /**
     * 发货（管理员）
     */
    @PostMapping("/{orderId}/ship")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<OrderResponse>> shipOrder(
            @PathVariable Long orderId,
            @RequestParam String trackingNumber,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("发货: orderId={}, trackingNumber={}, adminId={}", orderId, trackingNumber, currentUser.getId());
        
        OrderResponse response = orderService.shipOrder(orderId, trackingNumber, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("订单发货成功", response));
    }

    /**
     * 确认收货
     */
    @PostMapping("/{orderId}/confirm-delivery")
    public ResponseEntity<ApiResponse<OrderResponse>> confirmDelivery(
            @PathVariable Long orderId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("确认收货: orderId={}, userId={}", orderId, currentUser.getId());
        
        OrderResponse response = orderService.confirmDelivery(orderId, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("确认收货成功", response));
    }

    /**
     * 申请退款
     */
    @PostMapping("/{orderId}/refund/request")
    public ResponseEntity<ApiResponse<OrderResponse>> requestRefund(
            @PathVariable Long orderId,
            @RequestParam String reason,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("申请退款: orderId={}, reason={}, userId={}", orderId, reason, currentUser.getId());
        
        OrderResponse response = orderService.requestRefund(orderId, reason, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("退款申请提交成功", response));
    }

    /**
     * 处理退款（管理员）
     */
    @PostMapping("/{orderId}/refund/process")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<OrderResponse>> processRefund(
            @PathVariable Long orderId,
            @RequestParam BigDecimal refundAmount,
            @RequestParam(required = false) String remarks,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.info("处理退款: orderId={}, refundAmount={}, adminId={}", orderId, refundAmount, currentUser.getId());
        
        OrderResponse response = orderService.processRefund(orderId, refundAmount, remarks, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success("退款处理成功", response));
    }

    /**
     * 获取订单详情（包含商品项和状态历史）
     */
    @GetMapping("/{orderId}/details")
    public ResponseEntity<ApiResponse<OrderResponse>> getOrderDetails(
            @PathVariable Long orderId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取订单详情: orderId={}, userId={}", orderId, currentUser.getId());
        
        OrderResponse response = orderService.getOrderDetails(orderId);
        
        // 检查权限
        if (!response.getUserId().equals(currentUser.getId()) && 
            !currentUser.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"))) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(403, "无权限访问该订单"));
        }
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }

    /**
     * 检查订单是否可以取消
     */
    @GetMapping("/{orderId}/can-cancel")
    public ResponseEntity<ApiResponse<Boolean>> canCancelOrder(
            @PathVariable Long orderId,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("检查订单是否可以取消: orderId={}, userId={}", orderId, currentUser.getId());
        
        boolean canCancel = orderService.canCancelOrder(orderId, currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(canCancel));
    }

    /**
     * 获取订单统计信息（管理员）
     */
    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getOrderStatistics() {
        logger.debug("获取订单统计信息");
        
        Map<String, Object> statistics = orderService.getOrderStatistics();
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取用户订单统计
     */
    @GetMapping("/user/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserOrderStatistics(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户订单统计: userId={}", currentUser.getId());
        
        Map<String, Object> statistics = orderService.getUserOrderStatistics(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取用户购买历史
     */
    @GetMapping("/user/purchase-history")
    public ResponseEntity<ApiResponse<List<OrderResponse>>> getUserPurchaseHistory(
            @RequestParam(defaultValue = "10") int limit,
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户购买历史: userId={}, limit={}", currentUser.getId(), limit);
        
        List<OrderResponse> history = orderService.getUserPurchaseHistory(currentUser.getId(), limit);
        return ResponseEntity.ok(ApiResponse.success(history));
    }

    /**
     * 获取用户复购商品
     */
    @GetMapping("/user/repeat-purchases")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getUserRepeatPurchases(
            @AuthenticationPrincipal UserPrincipal currentUser) {
        logger.debug("获取用户复购商品: userId={}", currentUser.getId());
        
        List<Map<String, Object>> repeatPurchases = orderService.getUserRepeatPurchases(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(repeatPurchases));
    }
}
