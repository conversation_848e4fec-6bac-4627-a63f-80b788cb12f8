package com.ecommerce.orderservice.dto;

import com.ecommerce.orderservice.entity.ShoppingCart;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 购物车响应DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ShoppingCartResponse {

    private Long id;
    private Long userId;
    private ShoppingCart.CartStatus status;
    private Integer totalItems;
    private BigDecimal totalAmount;
    private List<CartItemResponse> items;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 构造函数
    public ShoppingCartResponse() {}

    // 静态工厂方法
    public static ShoppingCartResponse fromEntity(ShoppingCart cart) {
        ShoppingCartResponse response = new ShoppingCartResponse();
        response.setId(cart.getId());
        response.setUserId(cart.getUserId());
        response.setStatus(cart.getStatus());
        response.setTotalItems(cart.getTotalItems());
        response.setTotalAmount(cart.getTotalAmount());
        response.setCreatedAt(cart.getCreatedAt());
        response.setUpdatedAt(cart.getUpdatedAt());
        
        // 转换购物车商品项
        if (cart.getItems() != null) {
            response.setItems(cart.getItems().stream()
                    .map(CartItemResponse::fromEntity)
                    .collect(Collectors.toList()));
        }
        
        return response;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public ShoppingCart.CartStatus getStatus() {
        return status;
    }

    public void setStatus(ShoppingCart.CartStatus status) {
        this.status = status;
    }

    public Integer getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(Integer totalItems) {
        this.totalItems = totalItems;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<CartItemResponse> getItems() {
        return items;
    }

    public void setItems(List<CartItemResponse> items) {
        this.items = items;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 业务方法
    public boolean isEmpty() {
        return items == null || items.isEmpty();
    }

    public boolean isActive() {
        return status == ShoppingCart.CartStatus.ACTIVE;
    }

    @Override
    public String toString() {
        return "ShoppingCartResponse{" +
                "id=" + id +
                ", userId=" + userId +
                ", status=" + status +
                ", totalItems=" + totalItems +
                ", totalAmount=" + totalAmount +
                ", itemsCount=" + (items != null ? items.size() : 0) +
                '}';
    }
}
