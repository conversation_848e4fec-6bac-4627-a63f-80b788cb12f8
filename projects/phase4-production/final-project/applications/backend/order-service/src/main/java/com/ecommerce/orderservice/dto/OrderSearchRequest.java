package com.ecommerce.orderservice.dto;

import com.ecommerce.orderservice.entity.Order;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单搜索请求DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class OrderSearchRequest {

    private String orderNumber;
    private Long userId;
    private Order.OrderStatus status;
    private List<Order.OrderStatus> statuses;
    private Order.PaymentStatus paymentStatus;
    private List<Order.PaymentStatus> paymentStatuses;
    private Order.PaymentMethod paymentMethod;
    private List<Order.PaymentMethod> paymentMethods;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private String shippingName;
    private String shippingPhone;
    private String shippingAddress;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private String sortBy; // createdAt, totalAmount, updatedAt
    private String sortDirection; // asc, desc

    // 构造函数
    public OrderSearchRequest() {}

    // Getters and Setters
    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Order.OrderStatus getStatus() {
        return status;
    }

    public void setStatus(Order.OrderStatus status) {
        this.status = status;
    }

    public List<Order.OrderStatus> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Order.OrderStatus> statuses) {
        this.statuses = statuses;
    }

    public Order.PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Order.PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public List<Order.PaymentStatus> getPaymentStatuses() {
        return paymentStatuses;
    }

    public void setPaymentStatuses(List<Order.PaymentStatus> paymentStatuses) {
        this.paymentStatuses = paymentStatuses;
    }

    public Order.PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(Order.PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public List<Order.PaymentMethod> getPaymentMethods() {
        return paymentMethods;
    }

    public void setPaymentMethods(List<Order.PaymentMethod> paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public String getShippingName() {
        return shippingName;
    }

    public void setShippingName(String shippingName) {
        this.shippingName = shippingName;
    }

    public String getShippingPhone() {
        return shippingPhone;
    }

    public void setShippingPhone(String shippingPhone) {
        this.shippingPhone = shippingPhone;
    }

    public String getShippingAddress() {
        return shippingAddress;
    }

    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    @Override
    public String toString() {
        return "OrderSearchRequest{" +
                "orderNumber='" + orderNumber + '\'' +
                ", userId=" + userId +
                ", status=" + status +
                ", paymentStatus=" + paymentStatus +
                ", minAmount=" + minAmount +
                ", maxAmount=" + maxAmount +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                '}';
    }
}
