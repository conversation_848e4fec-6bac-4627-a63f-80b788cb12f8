package com.ecommerce.orderservice.service;

import com.ecommerce.orderservice.dto.OrderRequest;
import com.ecommerce.orderservice.dto.OrderResponse;
import com.ecommerce.orderservice.dto.OrderSearchRequest;
import com.ecommerce.orderservice.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface OrderService {

    /**
     * 创建订单
     */
    OrderResponse createOrder(Long userId, OrderRequest request);

    /**
     * 从购物车创建订单
     */
    OrderResponse createOrderFromCart(Long userId, OrderRequest request);

    /**
     * 根据ID获取订单
     */
    OrderResponse getOrderById(Long orderId);

    /**
     * 根据订单号获取订单
     */
    OrderResponse getOrderByNumber(String orderNumber);

    /**
     * 获取用户订单列表
     */
    Page<OrderResponse> getUserOrders(Long userId, Pageable pageable);

    /**
     * 根据状态获取用户订单
     */
    Page<OrderResponse> getUserOrdersByStatus(Long userId, Order.OrderStatus status, Pageable pageable);

    /**
     * 搜索订单
     */
    Page<OrderResponse> searchOrders(OrderSearchRequest request, Pageable pageable);

    /**
     * 获取所有订单（管理员）
     */
    Page<OrderResponse> getAllOrders(Pageable pageable);

    /**
     * 根据状态获取订单
     */
    Page<OrderResponse> getOrdersByStatus(Order.OrderStatus status, Pageable pageable);

    /**
     * 更新订单状态
     */
    OrderResponse updateOrderStatus(Long orderId, Order.OrderStatus status, String remarks, Long operatorId);

    /**
     * 取消订单
     */
    OrderResponse cancelOrder(Long orderId, String reason, Long userId);

    /**
     * 用户取消订单
     */
    OrderResponse cancelOrderByUser(Long orderId, String reason, Long userId);

    /**
     * 管理员取消订单
     */
    OrderResponse cancelOrderByAdmin(Long orderId, String reason, Long adminId);

    /**
     * 支付订单
     */
    OrderResponse payOrder(Long orderId, Order.PaymentMethod paymentMethod, String transactionId);

    /**
     * 发货
     */
    OrderResponse shipOrder(Long orderId, String trackingNumber, Long operatorId);

    /**
     * 确认收货
     */
    OrderResponse confirmDelivery(Long orderId, Long userId);

    /**
     * 申请退款
     */
    OrderResponse requestRefund(Long orderId, String reason, Long userId);

    /**
     * 处理退款
     */
    OrderResponse processRefund(Long orderId, BigDecimal refundAmount, String remarks, Long operatorId);

    /**
     * 获取订单详情（包含商品项和状态历史）
     */
    OrderResponse getOrderDetails(Long orderId);

    /**
     * 检查订单是否可以取消
     */
    boolean canCancelOrder(Long orderId, Long userId);

    /**
     * 检查订单是否可以支付
     */
    boolean canPayOrder(Long orderId);

    /**
     * 检查订单是否可以发货
     */
    boolean canShipOrder(Long orderId);

    /**
     * 检查订单是否可以确认收货
     */
    boolean canConfirmDelivery(Long orderId, Long userId);

    /**
     * 获取订单统计信息
     */
    Map<String, Object> getOrderStatistics();

    /**
     * 获取用户订单统计
     */
    Map<String, Object> getUserOrderStatistics(Long userId);

    /**
     * 获取时间范围内的订单统计
     */
    Map<String, Object> getOrderStatisticsByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取热销商品
     */
    List<Map<String, Object>> getBestSellingProducts(int limit);

    /**
     * 获取用户购买历史
     */
    List<OrderResponse> getUserPurchaseHistory(Long userId, int limit);

    /**
     * 获取用户复购商品
     */
    List<Map<String, Object>> getUserRepeatPurchases(Long userId);

    /**
     * 处理超时订单
     */
    void handleTimeoutOrders();

    /**
     * 自动确认收货
     */
    void autoConfirmDelivery();

    /**
     * 生成订单号
     */
    String generateOrderNumber();

    /**
     * 计算订单金额
     */
    BigDecimal calculateOrderAmount(OrderRequest request);

    /**
     * 验证订单数据
     */
    void validateOrderData(OrderRequest request);

    /**
     * 检查库存
     */
    boolean checkInventory(OrderRequest request);

    /**
     * 扣减库存
     */
    void reduceInventory(OrderRequest request);

    /**
     * 恢复库存
     */
    void restoreInventory(Long orderId);

    /**
     * 发送订单通知
     */
    void sendOrderNotification(Long orderId, String eventType);

    /**
     * 获取订单状态历史
     */
    List<Map<String, Object>> getOrderStatusHistory(Long orderId);

    /**
     * 批量更新订单状态
     */
    void batchUpdateOrderStatus(List<Long> orderIds, Order.OrderStatus status, String remarks, Long operatorId);

    /**
     * 导出订单数据
     */
    byte[] exportOrders(OrderSearchRequest request);

    /**
     * 获取订单报表数据
     */
    Map<String, Object> getOrderReportData(LocalDateTime startTime, LocalDateTime endTime);
}
