package com.ecommerce.orderservice.repository;

import com.ecommerce.orderservice.entity.CartItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 购物车商品项数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface CartItemRepository extends JpaRepository<CartItem, Long> {

    /**
     * 根据购物车ID查找所有商品项
     */
    List<CartItem> findByCartIdOrderByCreatedAtDesc(Long cartId);

    /**
     * 根据购物车ID和商品ID查找商品项
     */
    Optional<CartItem> findByCartIdAndProductId(Long cartId, Long productId);

    /**
     * 根据商品ID查找所有购物车项
     */
    List<CartItem> findByProductId(Long productId);

    /**
     * 根据用户ID查找所有购物车项（通过购物车关联）
     */
    @Query("SELECT ci FROM CartItem ci JOIN ci.cart c WHERE c.userId = :userId ORDER BY ci.createdAt DESC")
    List<CartItem> findByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和商品ID查找购物车项
     */
    @Query("SELECT ci FROM CartItem ci JOIN ci.cart c WHERE c.userId = :userId AND ci.productId = :productId")
    Optional<CartItem> findByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * 统计购物车中的商品项数量
     */
    long countByCartId(Long cartId);

    /**
     * 统计商品在购物车中的总数量
     */
    @Query("SELECT COALESCE(SUM(ci.quantity), 0) FROM CartItem ci WHERE ci.productId = :productId")
    Integer getTotalQuantityByProductId(@Param("productId") Long productId);

    /**
     * 删除购物车中的所有商品项
     */
    @Modifying
    @Query("DELETE FROM CartItem ci WHERE ci.cart.id = :cartId")
    int deleteByCartId(@Param("cartId") Long cartId);

    /**
     * 删除指定商品的所有购物车项
     */
    @Modifying
    @Query("DELETE FROM CartItem ci WHERE ci.productId = :productId")
    int deleteByProductId(@Param("productId") Long productId);

    /**
     * 批量删除过期的购物车项
     */
    @Modifying
    @Query("DELETE FROM CartItem ci WHERE ci.cart.id IN " +
           "(SELECT c.id FROM ShoppingCart c WHERE c.updatedAt < :expireTime)")
    int deleteExpiredCartItems(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查找热门商品（在购物车中出现频率高的商品）
     */
    @Query("SELECT ci.productId, COUNT(ci) as count FROM CartItem ci " +
           "GROUP BY ci.productId ORDER BY count DESC")
    List<Object[]> findPopularProducts();

    /**
     * 查找用户最近添加的购物车项
     */
    @Query("SELECT ci FROM CartItem ci JOIN ci.cart c WHERE c.userId = :userId " +
           "ORDER BY ci.createdAt DESC")
    List<CartItem> findRecentItemsByUserId(@Param("userId") Long userId);

    /**
     * 查找指定时间范围内添加的购物车项
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY ci.createdAt DESC")
    List<CartItem> findItemsByDateRange(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户购物车中的商品种类数
     */
    @Query("SELECT COUNT(DISTINCT ci.productId) FROM CartItem ci JOIN ci.cart c WHERE c.userId = :userId")
    Long countDistinctProductsByUserId(@Param("userId") Long userId);

    /**
     * 查找购物车中价格最高的商品项
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.cart.id = :cartId ORDER BY ci.unitPrice DESC")
    List<CartItem> findItemsByCartIdOrderByPriceDesc(@Param("cartId") Long cartId);

    /**
     * 查找购物车中数量最多的商品项
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.cart.id = :cartId ORDER BY ci.quantity DESC")
    List<CartItem> findItemsByCartIdOrderByQuantityDesc(@Param("cartId") Long cartId);

    /**
     * 检查商品是否在用户的购物车中
     */
    @Query("SELECT COUNT(ci) > 0 FROM CartItem ci JOIN ci.cart c " +
           "WHERE c.userId = :userId AND ci.productId = :productId")
    boolean existsByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * 获取用户购物车中特定商品的数量
     */
    @Query("SELECT COALESCE(ci.quantity, 0) FROM CartItem ci JOIN ci.cart c " +
           "WHERE c.userId = :userId AND ci.productId = :productId")
    Integer getQuantityByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);
}
