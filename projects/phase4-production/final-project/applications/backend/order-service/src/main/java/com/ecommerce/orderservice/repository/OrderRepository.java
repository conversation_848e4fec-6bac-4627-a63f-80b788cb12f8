package com.ecommerce.orderservice.repository;

import com.ecommerce.orderservice.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订单数据访问层
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {

    /**
     * 根据订单号查找订单
     */
    Optional<Order> findByOrderNumber(String orderNumber);

    /**
     * 检查订单号是否存在
     */
    boolean existsByOrderNumber(String orderNumber);

    /**
     * 根据用户ID查找订单（分页）
     */
    Page<Order> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找订单
     */
    Page<Order> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Order.OrderStatus status, Pageable pageable);

    /**
     * 根据状态查找订单
     */
    Page<Order> findByStatusOrderByCreatedAtDesc(Order.OrderStatus status, Pageable pageable);

    /**
     * 根据支付状态查找订单
     */
    Page<Order> findByPaymentStatusOrderByCreatedAtDesc(Order.PaymentStatus paymentStatus, Pageable pageable);

    /**
     * 根据支付方式查找订单
     */
    Page<Order> findByPaymentMethodOrderByCreatedAtDesc(Order.PaymentMethod paymentMethod, Pageable pageable);

    /**
     * 根据时间范围查找订单
     */
    @Query("SELECT o FROM Order o WHERE o.createdAt BETWEEN :startTime AND :endTime ORDER BY o.createdAt DESC")
    Page<Order> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      Pageable pageable);

    /**
     * 根据用户ID和时间范围查找订单
     */
    @Query("SELECT o FROM Order o WHERE o.userId = :userId AND o.createdAt BETWEEN :startTime AND :endTime ORDER BY o.createdAt DESC")
    Page<Order> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime,
                                               Pageable pageable);

    /**
     * 根据金额范围查找订单
     */
    @Query("SELECT o FROM Order o WHERE o.totalAmount BETWEEN :minAmount AND :maxAmount ORDER BY o.createdAt DESC")
    Page<Order> findByTotalAmountBetween(@Param("minAmount") BigDecimal minAmount,
                                        @Param("maxAmount") BigDecimal maxAmount,
                                        Pageable pageable);

    /**
     * 查找超时未支付的订单
     */
    @Query("SELECT o FROM Order o WHERE o.status = :status AND o.createdAt < :timeoutTime")
    List<Order> findTimeoutOrders(@Param("status") Order.OrderStatus status,
                                 @Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 查找需要自动确认收货的订单
     */
    @Query("SELECT o FROM Order o WHERE o.status = :status AND o.shippedTime < :autoConfirmTime")
    List<Order> findOrdersForAutoConfirm(@Param("status") Order.OrderStatus status,
                                        @Param("autoConfirmTime") LocalDateTime autoConfirmTime);

    /**
     * 统计用户订单数量
     */
    long countByUserId(Long userId);

    /**
     * 统计用户指定状态的订单数量
     */
    long countByUserIdAndStatus(Long userId, Order.OrderStatus status);

    /**
     * 统计指定状态的订单数量
     */
    long countByStatus(Order.OrderStatus status);

    /**
     * 统计指定支付状态的订单数量
     */
    long countByPaymentStatus(Order.PaymentStatus paymentStatus);

    /**
     * 计算用户订单总金额
     */
    @Query("SELECT COALESCE(SUM(o.totalAmount), 0) FROM Order o WHERE o.userId = :userId")
    BigDecimal sumTotalAmountByUserId(@Param("userId") Long userId);

    /**
     * 计算用户指定状态订单总金额
     */
    @Query("SELECT COALESCE(SUM(o.totalAmount), 0) FROM Order o WHERE o.userId = :userId AND o.status = :status")
    BigDecimal sumTotalAmountByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Order.OrderStatus status);

    /**
     * 计算时间范围内的订单总金额
     */
    @Query("SELECT COALESCE(SUM(o.totalAmount), 0) FROM Order o WHERE o.createdAt BETWEEN :startTime AND :endTime")
    BigDecimal sumTotalAmountByDateRange(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计各状态订单数量
     */
    @Query("SELECT o.status, COUNT(o) FROM Order o GROUP BY o.status")
    List<Object[]> countByStatus();

    /**
     * 统计各支付状态订单数量
     */
    @Query("SELECT o.paymentStatus, COUNT(o) FROM Order o GROUP BY o.paymentStatus")
    List<Object[]> countByPaymentStatus();

    /**
     * 统计各支付方式订单数量
     */
    @Query("SELECT o.paymentMethod, COUNT(o) FROM Order o WHERE o.paymentMethod IS NOT NULL GROUP BY o.paymentMethod")
    List<Object[]> countByPaymentMethod();

    /**
     * 查找用户最近的订单
     */
    @Query("SELECT o FROM Order o WHERE o.userId = :userId ORDER BY o.createdAt DESC")
    List<Order> findRecentOrdersByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找热销商品（根据订单项统计）
     */
    @Query("SELECT oi.productId, SUM(oi.quantity) as totalSold FROM Order o JOIN o.items oi " +
           "WHERE o.status IN ('PAID', 'SHIPPED', 'DELIVERED') " +
           "GROUP BY oi.productId ORDER BY totalSold DESC")
    List<Object[]> findBestSellingProducts();

    /**
     * 查找指定时间范围内的热销商品
     */
    @Query("SELECT oi.productId, SUM(oi.quantity) as totalSold FROM Order o JOIN o.items oi " +
           "WHERE o.status IN ('PAID', 'SHIPPED', 'DELIVERED') " +
           "AND o.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY oi.productId ORDER BY totalSold DESC")
    List<Object[]> findBestSellingProductsByDateRange(@Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查找用户购买过的商品
     */
    @Query("SELECT DISTINCT oi.productId FROM Order o JOIN o.items oi " +
           "WHERE o.userId = :userId AND o.status IN ('PAID', 'SHIPPED', 'DELIVERED')")
    List<Long> findPurchasedProductIdsByUserId(@Param("userId") Long userId);

    /**
     * 计算用户复购率
     */
    @Query("SELECT COUNT(DISTINCT o.userId) FROM Order o WHERE o.userId IN " +
           "(SELECT o2.userId FROM Order o2 GROUP BY o2.userId HAVING COUNT(o2) > 1)")
    Long countRepeatCustomers();

    /**
     * 查找大额订单
     */
    @Query("SELECT o FROM Order o WHERE o.totalAmount >= :minAmount ORDER BY o.totalAmount DESC")
    List<Order> findHighValueOrders(@Param("minAmount") BigDecimal minAmount);

    /**
     * 查找可以取消的订单
     */
    @Query("SELECT o FROM Order o WHERE o.status IN ('PENDING', 'PAID') AND o.userId = :userId")
    List<Order> findCancellableOrdersByUserId(@Param("userId") Long userId);
}
