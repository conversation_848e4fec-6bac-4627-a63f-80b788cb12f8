package com.ecommerce.orderservice.dto;

import com.ecommerce.orderservice.entity.Order;
import com.ecommerce.orderservice.entity.OrderStatusHistory;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 订单状态历史响应DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class OrderStatusHistoryResponse {

    private Long id;
    private Order.OrderStatus fromStatus;
    private String fromStatusDescription;
    private Order.OrderStatus toStatus;
    private String toStatusDescription;
    private String remarks;
    private String operator;
    private Long operatorId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // 构造函数
    public OrderStatusHistoryResponse() {}

    // 静态工厂方法
    public static OrderStatusHistoryResponse fromEntity(OrderStatusHistory history) {
        OrderStatusHistoryResponse response = new OrderStatusHistoryResponse();
        response.setId(history.getId());
        response.setFromStatus(history.getFromStatus());
        if (history.getFromStatus() != null) {
            response.setFromStatusDescription(history.getFromStatus().getDescription());
        }
        response.setToStatus(history.getToStatus());
        response.setToStatusDescription(history.getToStatus().getDescription());
        response.setRemarks(history.getRemarks());
        response.setOperator(history.getOperator());
        response.setOperatorId(history.getOperatorId());
        response.setCreatedAt(history.getCreatedAt());
        return response;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Order.OrderStatus getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(Order.OrderStatus fromStatus) {
        this.fromStatus = fromStatus;
    }

    public String getFromStatusDescription() {
        return fromStatusDescription;
    }

    public void setFromStatusDescription(String fromStatusDescription) {
        this.fromStatusDescription = fromStatusDescription;
    }

    public Order.OrderStatus getToStatus() {
        return toStatus;
    }

    public void setToStatus(Order.OrderStatus toStatus) {
        this.toStatus = toStatus;
    }

    public String getToStatusDescription() {
        return toStatusDescription;
    }

    public void setToStatusDescription(String toStatusDescription) {
        this.toStatusDescription = toStatusDescription;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "OrderStatusHistoryResponse{" +
                "id=" + id +
                ", fromStatus=" + fromStatus +
                ", toStatus=" + toStatus +
                ", operator='" + operator + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
